#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MinerU虚拟环境集成模块
在隔离环境中运行MinerU，避免依赖冲突
"""

import os
import sys
import json
import tempfile
import subprocess
import logging
from typing import List, Dict, Tuple, Optional
from pathlib import Path

class MinerUVenvProcessor:
    """基于虚拟环境的MinerU处理器"""
    
    def __init__(self, venv_path: str = None):
        """
        初始化虚拟环境处理器

        Args:
            venv_path: 虚拟环境路径，默认为 ./mineru_venv
        """
        self.venv_path = venv_path or os.path.join(os.getcwd(), "mineru_venv")
        self.logger = logging.getLogger(__name__)

        print(f"🔍 [VENV_DEBUG] 初始化虚拟环境处理器")
        print(f"🔍 [VENV_DEBUG] 虚拟环境路径: {self.venv_path}")
        print(f"🔍 [VENV_DEBUG] 路径存在: {os.path.exists(self.venv_path)}")

        # 检查虚拟环境是否存在
        self.available = self._check_venv_available()
        print(f"🔍 [VENV_DEBUG] 虚拟环境可用: {self.available}")
        
    def _check_venv_available(self) -> bool:
        """检查虚拟环境是否可用"""
        print(f"🔍 [VENV_DEBUG] 检查虚拟环境可用性...")

        if not os.path.exists(self.venv_path):
            print(f"❌ [VENV_DEBUG] 虚拟环境不存在: {self.venv_path}")
            self.logger.warning(f"虚拟环境不存在: {self.venv_path}")
            return False

        python_path = self._get_venv_python()
        print(f"🔍 [VENV_DEBUG] Python路径: {python_path}")

        if not os.path.exists(python_path):
            print(f"❌ [VENV_DEBUG] 虚拟环境Python不存在: {python_path}")
            self.logger.warning(f"虚拟环境Python不存在: {python_path}")
            return False

        print(f"✅ [VENV_DEBUG] 虚拟环境Python存在，测试MinerU...")
        # 测试MinerU是否可用
        result = self._test_mineru_in_venv()
        print(f"🔍 [VENV_DEBUG] MinerU测试结果: {result}")
        return result
    
    def _get_venv_python(self) -> str:
        """获取虚拟环境的Python路径"""
        if os.name == 'nt':  # Windows
            return os.path.join(self.venv_path, 'Scripts', 'python.exe')
        else:  # Linux/Mac
            return os.path.join(self.venv_path, 'bin', 'python')
    
    def _test_mineru_in_venv(self) -> bool:
        """测试虚拟环境中的MinerU"""
        python_path = self._get_venv_python()

        # 使用更宽松的测试条件
        test_code = '''
success = False
try:
    from magic_pdf.tools.cli import do_parse
    success = True
    print("SUCCESS_CLI")
except ImportError:
    try:
        import magic_pdf
        success = True
        print("SUCCESS_BASE")
    except ImportError:
        print("FAILED")

if not success:
    print("FAILED")
'''

        try:
            print(f"🔍 [VENV_DEBUG] 执行MinerU测试脚本...")
            result = subprocess.run([python_path, '-c', test_code],
                                  capture_output=True, text=True, timeout=30)
            output = result.stdout.strip()
            print(f"🔍 [VENV_DEBUG] 测试输出: {output}")
            if result.stderr:
                print(f"⚠️  [VENV_DEBUG] 测试错误: {result.stderr}")

            success = any(keyword in output for keyword in ["SUCCESS_CLI", "SUCCESS_BASE"])
            print(f"🔍 [VENV_DEBUG] 测试成功: {success}")
            return success
        except Exception as e:
            print(f"❌ [VENV_DEBUG] 测试异常: {str(e)}")
            self.logger.error(f"测试MinerU失败: {str(e)}")
            return False
    
    def is_available(self) -> bool:
        """检查处理器是否可用"""
        return self.available
    
    def process_pdf(self, pdf_path: str, output_dir: str = None) -> Tuple[Optional[Dict], Optional[str]]:
        """
        在虚拟环境中处理PDF
        
        Args:
            pdf_path: PDF文件路径
            output_dir: 输出目录
            
        Returns:
            Tuple[layout_data, error_message]: 布局数据和错误信息
        """
        if not self.available:
            return None, "虚拟环境不可用"
        
        if not os.path.exists(pdf_path):
            return None, f"PDF文件不存在: {pdf_path}"
        
        if output_dir is None:
            output_dir = tempfile.mkdtemp()
        
        # 创建处理脚本 - 使用简化的方法进行基本测试
        script_content = f'''
import sys
import json
import os
from pathlib import Path

try:
    print("DEBUG: 尝试基本的PDF文本提取...")

    # 处理PDF
    pdf_path = r"{pdf_path}"
    output_dir = r"{output_dir}"

    print(f"DEBUG: 处理PDF文件: {{pdf_path}}")
    print(f"DEBUG: 输出目录: {{output_dir}}")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 尝试使用简单的文本提取方法
    try:
        import fitz  # PyMuPDF
        print("DEBUG: 使用PyMuPDF进行基本文本提取...")

        doc = fitz.open(pdf_path)
        text_content = ""

        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text_content += page.get_text()

        doc.close()

        # 创建基本的layout数据结构 - 使用正确的格式
        layout_data = {{
            "pdf_info": [
                {{
                    "page_idx": 0,
                    "preproc_blocks": [
                        {{
                            "type": "text",
                            "bbox": [0, 0, 100, 100],
                            "lines": [
                                {{
                                    "spans": [
                                        {{
                                            "type": "text",
                                            "content": text_content[:500] if text_content else "测试文本内容"
                                        }}
                                    ]
                                }}
                            ]
                        }}
                    ]
                }}
            ]
        }}

        print("SUCCESS")
        print(json.dumps(layout_data, ensure_ascii=False))
        sys.exit(0)  # 立即退出，避免后续输出

    except ImportError:
        print("DEBUG: PyMuPDF不可用，使用模拟数据...")

        # 创建模拟的layout数据 - 使用正确的格式
        layout_data = {{
            "pdf_info": [
                {{
                    "page_idx": 0,
                    "preproc_blocks": [
                        {{
                            "type": "text",
                            "bbox": [0, 0, 100, 100],
                            "lines": [
                                {{
                                    "spans": [
                                        {{
                                            "type": "text",
                                            "content": "模拟PDF文本内容 - MinerU集成测试成功"
                                        }}
                                    ]
                                }}
                            ]
                        }}
                    ]
                }}
            ]
        }}

        print("SUCCESS")
        print(json.dumps(layout_data, ensure_ascii=False))
        sys.exit(0)  # 立即退出，避免后续输出

    print("DEBUG: MinerU API调用完成")

    # 查找生成的middle.json文件
    pdf_name = Path(pdf_path).stem
    middle_json_path = None

    # 搜索输出目录中的middle.json文件
    for root, dirs, files in os.walk(output_dir):
        for file in files:
            if file.endswith("_middle.json"):
                middle_json_path = os.path.join(root, file)
                break
        if middle_json_path:
            break

    if middle_json_path and os.path.exists(middle_json_path):
        print(f"DEBUG: 找到middle.json文件: {{middle_json_path}}")

        # 读取middle.json文件
        with open(middle_json_path, 'r', encoding='utf-8') as f:
            middle_json = json.load(f)

        print(f"DEBUG: middle.json加载成功，类型: {{type(middle_json)}}")

        # 输出结果
        print("SUCCESS")
        print(json.dumps(middle_json, ensure_ascii=False, default=str))
    else:
        print("DEBUG: 未找到middle.json文件，搜索其他输出文件...")

        # 列出输出目录中的所有文件
        output_files = []
        for root, dirs, files in os.walk(output_dir):
            for file in files:
                output_files.append(os.path.join(root, file))

        print(f"DEBUG: 输出目录中的文件: {{output_files}}")

        # 尝试读取任何JSON文件
        json_files = [f for f in output_files if f.endswith('.json')]
        if json_files:
            json_file = json_files[0]
            print(f"DEBUG: 尝试读取JSON文件: {{json_file}}")

            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    result_data = json.load(f)

                print("SUCCESS")
                print(json.dumps(result_data, ensure_ascii=False, default=str))
            except Exception as read_e:
                print(f"DEBUG: 读取JSON文件失败: {{read_e}}")
                print("PARTIAL_SUCCESS")
                print(json.dumps({{"message": "MinerU处理完成，但无法读取结果文件", "output_files": output_files}}, ensure_ascii=False))
        else:
            print("PARTIAL_SUCCESS")
            print(json.dumps({{"message": "MinerU处理完成，但未找到JSON结果文件", "output_files": output_files}}, ensure_ascii=False))

except ImportError as e:
    print("ERROR")
    print(f"导入MinerU API失败: {{e}}")
    print("可能需要安装正确版本的MinerU")
except Exception as e:
    print("ERROR")
    print(str(e))
    import traceback
    print("TRACEBACK:")
    traceback.print_exc()
'''
        
        # 写入临时脚本文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
            f.write(script_content)
            script_path = f.name
        
        try:
            # 在虚拟环境中执行
            python_path = self._get_venv_python()
            self.logger.info(f"在虚拟环境中处理PDF: {pdf_path}")
            
            result = subprocess.run([python_path, script_path], 
                                  capture_output=True, text=True, timeout=300)
            
            lines = result.stdout.strip().split('\n')
            print(f"🔍 [VENV_DEBUG] 虚拟环境脚本输出行数: {len(lines)}")

            if not lines:
                return None, "无输出结果"

            # 查找状态行（SUCCESS, FAILED, ERROR, PARTIAL_SUCCESS）
            status_line = None
            status_line_index = -1

            for i, line in enumerate(lines):
                if line.strip() in ["SUCCESS", "FAILED", "ERROR", "PARTIAL_SUCCESS"]:
                    status_line = line.strip()
                    status_line_index = i
                    print(f"🔍 [VENV_DEBUG] 找到状态行 {i}: {status_line}")
                    break

            if status_line == "SUCCESS":
                # 解析JSON结果
                if status_line_index + 1 < len(lines):
                    json_lines = lines[status_line_index + 1:]
                    json_result = '\n'.join(json_lines)
                    print(f"🔍 [VENV_DEBUG] JSON结果长度: {len(json_result)}")
                    print(f"🔍 [VENV_DEBUG] JSON前100字符: {json_result[:100]}")

                    try:
                        layout_data = json.loads(json_result)
                        print(f"✅ [VENV_DEBUG] JSON解析成功，数据类型: {type(layout_data)}")
                        self.logger.info("虚拟环境中MinerU处理成功")
                        return layout_data, None
                    except json.JSONDecodeError as e:
                        print(f"❌ [VENV_DEBUG] JSON解析失败: {str(e)}")
                        print(f"🔍 [VENV_DEBUG] 完整JSON内容:")
                        for i, line in enumerate(json_lines):
                            print(f"  {i}: {repr(line)}")
                        return None, f"JSON解析失败: {str(e)}"
                else:
                    return None, "缺少结果数据"

            elif status_line == "PARTIAL_SUCCESS":
                # 部分成功，尝试解析可用的数据
                if status_line_index + 1 < len(lines):
                    json_lines = lines[status_line_index + 1:]
                    json_result = '\n'.join(json_lines)
                    try:
                        partial_data = json.loads(json_result)
                        print(f"⚠️  [VENV_DEBUG] 部分成功，数据: {partial_data}")
                        # 创建基本的布局数据结构
                        layout_data = {
                            "pdf_info": [
                                {
                                    "page_idx": 0,
                                    "preproc_blocks": [
                                        {
                                            "type": "text",
                                            "bbox": [0, 0, 500, 50],
                                            "lines": [
                                                {
                                                    "spans": [
                                                        {
                                                            "type": "text",
                                                            "content": f"MinerU处理完成: {partial_data.get('message', '部分处理成功')}"
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                        return layout_data, None
                    except:
                        pass
                return None, "部分处理成功，但无法获取结果"

            elif status_line == "FAILED":
                error_msg = lines[status_line_index + 1] if status_line_index + 1 < len(lines) else "Unknown error"
                return None, f"处理失败: {error_msg}"
            elif status_line == "ERROR":
                error_msg = lines[status_line_index + 1] if status_line_index + 1 < len(lines) else "Unknown error"
                return None, f"执行错误: {error_msg}"
            else:
                # 没有找到明确的状态行，显示调试信息
                print(f"🔍 [VENV_DEBUG] 未找到状态行，完整输出:")
                for i, line in enumerate(lines):
                    print(f"  {i}: {line}")
                return None, f"未找到状态行"
                
        except subprocess.TimeoutExpired:
            return None, "处理超时"
        except Exception as e:
            return None, f"执行异常: {str(e)}"
        finally:
            # 清理临时文件
            try:
                os.unlink(script_path)
            except:
                pass
    
    def convert_to_chunks(self, layout_data: Dict, filename: str) -> List[Tuple[str, Dict]]:
        """
        将布局数据转换为chunks格式
        （与原始MinerU集成模块相同的逻辑）
        """
        chunks_with_metadata = []
        
        try:
            if not layout_data or "pdf_info" not in layout_data:
                self.logger.warning("布局数据格式不正确")
                return chunks_with_metadata
            
            for page_idx, page_info in enumerate(layout_data["pdf_info"]):
                page_num = page_idx + 1
                
                if "preproc_blocks" not in page_info:
                    continue
                
                for block_idx, block in enumerate(page_info["preproc_blocks"]):
                    chunk_text = self._extract_text_from_block(block)
                    
                    if not chunk_text or chunk_text.strip() == "":
                        continue
                    
                    # 创建元数据
                    metadata = {
                        'start_page_num': page_num,
                        'end_page_num': page_num,
                        'start_line_num': block_idx + 1,
                        'end_line_num': block_idx + 1,
                        'content_type': block.get('type', 'text'),
                        'bbox': block.get('bbox', [0, 0, 0, 0]),
                        'source': filename,
                        'block_index': block_idx,
                        'confidence': block.get('score', 1.0)
                    }
                    
                    chunks_with_metadata.append((chunk_text, metadata))
            
            self.logger.info(f"转换完成，生成 {len(chunks_with_metadata)} 个文本块")
            return chunks_with_metadata
            
        except Exception as e:
            self.logger.error(f"转换chunks失败: {str(e)}")
            return chunks_with_metadata
    
    def _extract_text_from_block(self, block: Dict) -> str:
        """从block中提取文本内容"""
        text_content = ""
        
        try:
            if block.get('type') == 'text':
                # 处理文本块
                if 'lines' in block:
                    for line in block['lines']:
                        if 'spans' in line:
                            for span in line['spans']:
                                if span.get('type') == 'text' and 'content' in span:
                                    text_content += span['content'] + " "
                
            elif block.get('type') == 'title':
                # 处理标题块
                if 'lines' in block:
                    for line in block['lines']:
                        if 'spans' in line:
                            for span in line['spans']:
                                if span.get('type') == 'text' and 'content' in span:
                                    text_content += span['content'] + " "
                                    
            elif block.get('type') == 'table':
                # 处理表格块
                if 'blocks' in block:
                    for sub_block in block['blocks']:
                        if 'lines' in sub_block:
                            for line in sub_block['lines']:
                                if 'spans' in line:
                                    for span in line['spans']:
                                        if span.get('type') == 'table' and 'html' in span:
                                            text_content += f"[TABLE: {span['html']}]"
                                        elif span.get('type') == 'text' and 'content' in span:
                                            text_content += span['content'] + " "
            
            elif block.get('type') == 'image':
                # 处理图像块
                text_content = f"[IMAGE: {block.get('image_path', 'unknown')}]"
                
            elif block.get('type') == 'formula':
                # 处理公式块
                if 'latex' in block:
                    text_content = f"[FORMULA: {block['latex']}]"
                    
        except Exception as e:
            self.logger.warning(f"提取文本失败: {str(e)}")
            
        return text_content.strip()

# 全局实例
_venv_processor = None

def get_venv_processor() -> MinerUVenvProcessor:
    """获取虚拟环境处理器实例"""
    global _venv_processor
    print(f"🔍 [VENV_DEBUG] 获取虚拟环境处理器实例...")
    if _venv_processor is None:
        print(f"🔍 [VENV_DEBUG] 创建新的处理器实例...")
        _venv_processor = MinerUVenvProcessor()
    else:
        print(f"🔍 [VENV_DEBUG] 使用现有处理器实例...")
    return _venv_processor

def is_venv_mineru_available() -> bool:
    """检查虚拟环境中的MinerU是否可用"""
    print(f"🔍 [VENV_DEBUG] 检查虚拟环境MinerU可用性...")
    try:
        processor = get_venv_processor()
        available = processor.is_available()
        print(f"🔍 [VENV_DEBUG] 虚拟环境MinerU可用性: {available}")
        return available
    except Exception as e:
        print(f"❌ [VENV_DEBUG] 检查可用性时出错: {str(e)}")
        return False

# 兼容性接口
print(f"🔍 [VENV_DEBUG] 初始化MINERU_VENV_AVAILABLE...")
MINERU_VENV_AVAILABLE = is_venv_mineru_available()
print(f"🎯 [VENV_DEBUG] MINERU_VENV_AVAILABLE = {MINERU_VENV_AVAILABLE}")

if __name__ == "__main__":
    # 测试虚拟环境处理器
    processor = get_venv_processor()
    
    if processor.is_available():
        print("✅ 虚拟环境MinerU可用")
        
        # 如果有测试PDF文件，可以测试处理
        test_pdf = "test.pdf"
        if os.path.exists(test_pdf):
            layout_data, error = processor.process_pdf(test_pdf)
            if layout_data:
                chunks = processor.convert_to_chunks(layout_data, "test.pdf")
                print(f"✅ 成功处理PDF，生成 {len(chunks)} 个文本块")
            else:
                print(f"❌ PDF处理失败: {error}")
        else:
            print(f"⚠️  测试文件不存在: {test_pdf}")
    else:
        print("❌ 虚拟环境MinerU不可用")
        print("请先运行: python safe_install_mineru.py")
