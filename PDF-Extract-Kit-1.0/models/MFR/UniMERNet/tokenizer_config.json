{"added_tokens_decoder": {"0": {"content": "<s>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "1": {"content": "<pad>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "2": {"content": "</s>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "3": {"content": "<unk>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "4": {"content": "[START_REF]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "5": {"content": "[END_REF]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "6": {"content": "[IMAGE]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "7": {"content": "<fragments>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "8": {"content": "</fragments>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "9": {"content": "<work>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "10": {"content": "</work>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "11": {"content": "[START_SUP]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "12": {"content": "[END_SUP]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "13": {"content": "[START_SUB]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "14": {"content": "[END_SUB]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "15": {"content": "[START_DNA]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "16": {"content": "[END_DNA]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "17": {"content": "[START_AMINO]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "18": {"content": "[END_AMINO]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "19": {"content": "[START_SMILES]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "20": {"content": "[END_SMILES]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "21": {"content": "[START_I_SMILES]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "22": {"content": "[END_I_SMILES]", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "additional_special_tokens": [], "bos_token": "<s>", "clean_up_tokenization_spaces": false, "eos_token": "</s>", "max_length": 4096, "model_max_length": 768, "pad_to_multiple_of": null, "pad_token": "<pad>", "pad_token_type_id": 0, "padding_side": "right", "processor_class": "VariableDonutProcessor", "stride": 0, "tokenizer_class": "NougatTokenizer", "truncation_side": "right", "truncation_strategy": "longest_first", "unk_token": "<unk>", "vocab_file": null}