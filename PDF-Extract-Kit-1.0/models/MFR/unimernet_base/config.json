{"_name_or_path": "unimernet/checkpoint-300000", "architectures": ["VisionEncoderDecoderModel"], "decoder": {"_name_or_path": "", "activation_dropout": 0.0, "activation_function": "gelu", "add_cross_attention": true, "add_final_layer_norm": true, "architectures": null, "attention_dropout": 0.0, "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": 0, "chunk_size_feed_forward": 0, "classifier_dropout": 0.0, "cross_attention_hidden_size": null, "d_model": 1024, "decoder_attention_heads": 16, "decoder_ffn_dim": 4096, "decoder_layerdrop": 0.0, "decoder_layers": 8, "decoder_start_token_id": null, "diversity_penalty": 0.0, "do_sample": false, "dropout": 0.1, "early_stopping": false, "encoder_attention_heads": 16, "encoder_ffn_dim": 4096, "encoder_layerdrop": 0.0, "encoder_layers": 12, "encoder_no_repeat_ngram_size": 0, "eos_token_id": 2, "exponential_decay_length_penalty": null, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": 2, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "init_std": 0.02, "is_decoder": true, "is_encoder_decoder": false, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "length_penalty": 1.0, "max_length": 20, "max_position_embeddings": 1536, "min_length": 0, "model_type": "mbart", "no_repeat_ngram_size": 0, "num_beam_groups": 1, "num_beams": 1, "num_hidden_layers": 12, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": 1, "prefix": null, "problem_type": null, "pruned_heads": {}, "remove_invalid_values": false, "repetition_penalty": 1.0, "return_dict": true, "return_dict_in_generate": false, "scale_embedding": true, "sep_token_id": null, "suppress_tokens": null, "task_specific_params": null, "temperature": 1.0, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": false, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "torch_dtype": null, "torchscript": false, "typical_p": 1.0, "use_bfloat16": false, "use_cache": true, "vocab_size": 50000}, "decoder_start_token_id": 0, "encoder": {"_name_or_path": "", "add_cross_attention": false, "architectures": null, "attention_probs_dropout_prob": 0.0, "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": null, "chunk_size_feed_forward": 0, "cross_attention_hidden_size": null, "decoder_start_token_id": null, "depths": [6, 6, 6, 6], "diversity_penalty": 0.0, "do_sample": false, "drop_path_rate": 0.1, "early_stopping": false, "embed_dim": 128, "encoder_no_repeat_ngram_size": 0, "eos_token_id": null, "exponential_decay_length_penalty": null, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "hidden_act": "gelu", "hidden_dropout_prob": 0.0, "hidden_size": 1024, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "image_size": [420, 420], "initializer_range": 0.02, "is_decoder": false, "is_encoder_decoder": false, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "layer_norm_eps": 1e-05, "length_penalty": 1.0, "max_length": 20, "min_length": 0, "mlp_ratio": 4.0, "model_type": "donut-swin", "no_repeat_ngram_size": 0, "num_beam_groups": 1, "num_beams": 1, "num_channels": 3, "num_heads": [4, 8, 16, 32], "num_layers": 4, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": null, "patch_size": 4, "path_norm": true, "prefix": null, "problem_type": null, "pruned_heads": {}, "qkv_bias": true, "remove_invalid_values": false, "repetition_penalty": 1.0, "return_dict": true, "return_dict_in_generate": false, "sep_token_id": null, "suppress_tokens": null, "task_specific_params": null, "temperature": 1.0, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": true, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "torch_dtype": null, "torchscript": false, "typical_p": 1.0, "use_2d_embeddings": false, "use_absolute_embeddings": false, "use_bfloat16": false, "window_size": 5}, "is_encoder_decoder": true, "model_type": "vision-encoder-decoder", "pad_token_id": 1, "tie_word_embeddings": false, "torch_dtype": "float16", "transformers_version": "4.36.0"}