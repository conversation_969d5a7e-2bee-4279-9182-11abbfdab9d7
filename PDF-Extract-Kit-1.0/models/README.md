Put [model files]() here:

```
models
├── Layout
│   ├── LayoutLMv3
│   │   ├── config.json
│   │   └── model_final.pth
│   └── YOLO
│       └── yolov10l_ft.pt
├── MFD
│   └── YOLO
│       └── yolo_v8_ft.pt
├── MFR
│   ├── UniMERNet
│   │   ├── README.md
│   │   ├── config.json
│   │   ├── preprocessor_config.json
│   │   ├── pytorch_model.bin
│   │   ├── tokenizer.json
│   │   └── tokenizer_config.json
│   ├── unimernet_base
│   │   ├── README.md
│   │   ├── config.json
│   │   ├── configuration.json
│   │   ├── preprocessor_config.json
│   │   ├── pytorch_model.pth
│   │   ├── tokenizer.json
│   │   ├── tokenizer_config.json
│   │   └── unimernet_base.yaml
│   ├── unimernet_small
│   │   ├── README.md
│   │   ├── config.json
│   │   ├── configuration.json
│   │   ├── preprocessor_config.json
│   │   ├── pytorch_model.pth
│   │   ├── tokenizer.json
│   │   ├── tokenizer_config.json
│   │   └── unimernet_small.yaml
│   └── unimernet_tiny
│       ├── README.md
│       ├── config.json
│       ├── configuration.json
│       ├── preprocessor_config.json
│       ├── pytorch_model.pth
│       ├── tokenizer.json
│       ├── tokenizer_config.json
│       └── unimernet_tiny.yaml
├── README.md
└── TabRec
    ├── StructEqTable
    │   ├── added_tokens.json
    │   ├── config.json
    │   ├── configuration_intern_vit.py
    │   ├── configuration_internvl_chat.py
    │   ├── conversation.py
    │   ├── generation_config.json
    │   ├── merges.txt
    │   ├── modeling_intern_vit.py
    │   ├── modeling_internvl_chat.py
    │   ├── model.safetensors
    │   ├── preprocessor_config.json
    │   ├── README.md
    │   ├── special_tokens_map.json
    │   ├── tokenizer_config.json
    │   └── vocab.json
    └── TableMaster
        ├── ch_PP-OCRv3_det_infer
        │   ├── inference.pdiparams
        │   ├── inference.pdiparams.info
        │   └── inference.pdmodel
        ├── ch_PP-OCRv3_rec_infer
        │   ├── inference.pdiparams
        │   ├── inference.pdiparams.info
        │   └── inference.pdmodel
        ├── ppocr_keys_v1.txt
        ├── table_master_structure_dict.txt
        └── table_structure_tablemaster_infer
            ├── inference.pdiparams
            ├── inference.pdiparams.info
            └── inference.pdmodel
```
