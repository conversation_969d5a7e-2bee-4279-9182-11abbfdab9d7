"""

    Module Name :           chatbot
    Last Modified Date :    23 Jan 2024

"""
import json
import re
import shutil
import subprocess
import base64

# from PyQt5.QtWebEngineWidgets import QWebEngineView
from flask import Flask, render_template, request, jsonify, send_from_directory, session, redirect, url_for, flash
import os
import pandas as pd

from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

import warnings

# import llama3_module
import mixtral_module
import mixtral_module_deepseek_nochathistory
import mixtral_module_openai
import mixtral_module_openvino
# import mixtral_module_retriever_to_text
from login import save_user_credentials, load_user_credentials

warnings.filterwarnings('ignore')

# Import Self-defined Libraries
import env
import preprocessing
import prompt_module
import vectorstore_module_newui
from graphstore_module_newui import graphstore_module_newui
import openai_module
import llama_module
# import mixtral_module_pipeline
import init_interface
import test_module
import webbrowser
import threading
from langchain.document_loaders import DirectoryLoader
from langchain.memory import ChatMessageHistory

from werkzeug.serving import make_server
# from PyQt5.QtWidgets import QApplication, QMainWindow
# from PyQt5.QtCore import QThread, QUrl
import sys
import datetime as dt
from typing import List, Tuple, Dict

datetime_userid_format = "%Y%m%d-%H-%M-%S"

# 文本块重叠配置
CHUNK_OVERLAP_WORDS = 15  # 重叠的单词数量（适用于英文）
CHUNK_OVERLAP_CHARS = 80  # 重叠的字符数量（适用于中文）

# =============================================================================
# TWO-STAGE LLM REFERENCE ANALYSIS SYSTEM
# =============================================================================

import re
import logging
import json
from typing import Dict, List, Tuple, Any

# Configure logging for reference analysis
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def generate_llm_based_references(user_question: str, bot_response: str, chunks_list: List[Dict], model_para: dict,
                                  prime_model_name: str) -> Dict:
    """
    Use LLM to analyze which chunks were most relevant to the generated response
    """

    # Prepare chunks information for analysis
    chunks_info = []
    for i, chunk in enumerate(chunks_list):
        # Get a preview of chunk content
        content = chunk.get('content', '')
        content_preview = content[:300] + '...' if len(content) > 300 else content

        chunks_info.append({
            'chunk_id': i,
            'source': chunk.get('source', 'Unknown'),
            'page': chunk.get('page_num', 'Unknown'),
            'content_preview': content_preview,
            'content_type': chunk.get('content_type', 'text'),
            'full_content_length': len(content)
        })

    # Create analysis prompt
    analysis_prompt = f"""
TASK: Analyze which source chunks were most relevant for answering the user's question.

USER QUESTION: {user_question}

GENERATED ANSWER: {bot_response}

AVAILABLE SOURCE CHUNKS:
{json.dumps(chunks_info, indent=2, ensure_ascii=False)}

Please analyze and provide:
1. Rank the chunks by their relevance to answering the question (1 = most relevant)
2. For each relevant chunk, explain WHY it was important for the answer
3. Identify any chunks that were NOT used or relevant
4. Provide a confidence score (0-100) for each chunk's contribution

IMPORTANT: Return ONLY a valid JSON response with this exact format:
{{
    "source_analysis": [
        {{
            "chunk_id": 0,
            "source_name": "document.pdf",
            "page": 1,
            "relevance_rank": 1,
            "confidence_score": 95,
            "contribution_explanation": "This source provided the main definition and core concepts",
            "was_used": true
        }}
    ],
    "overall_assessment": "Brief summary of source usage"
}}
"""

    # Call the same LLM for analysis
    try:
        print(f"🤖 Starting LLM-based reference analysis using {prime_model_name}...")

        if prime_model_name.lower() == "openai":
            analysis_response = openai_module.openai_response(
                analysis_prompt,
                "You are an expert analyst. Analyze source relevance and provide detailed analysis in JSON format only.",
                "", "", model_para, None, None
            )
        elif prime_model_name.lower() == "mixtral":
            analysis_response = mixtral_module_deepseek_nochathistory.mixtral_response(
                analysis_prompt,
                "You are an expert analyst. Analyze source relevance and provide detailed analysis in JSON format only.",
                "", "", model_para, None, None
            )
        elif prime_model_name.lower() == "llama":
            analysis_response = llama_module.llama_response(
                analysis_prompt,
                "You are an expert analyst. Analyze source relevance and provide detailed analysis in JSON format only.",
                "", "", model_para, None, None
            )
        else:
            print(f"❌ Unsupported model for reference analysis: {prime_model_name}")
            return None

        print(f"📋 LLM analysis response length: {len(analysis_response)} chars")

        # Parse the JSON response
        try:
            # Extract JSON from response if wrapped in text
            json_start = analysis_response.find('{')
            json_end = analysis_response.rfind('}') + 1
            if json_start != -1 and json_end != -1:
                json_str = analysis_response[json_start:json_end]
                analysis_data = json.loads(json_str)

                print(f"✅ Successfully parsed LLM reference analysis")
                print(f"📊 Found analysis for {len(analysis_data.get('source_analysis', []))} chunks")

                return analysis_data
            else:
                print("❌ No valid JSON found in LLM response")
                print(f"Response preview: {analysis_response[:200]}...")
                return None

        except json.JSONDecodeError as e:
            print(f"❌ Failed to parse LLM analysis response as JSON: {e}")
            print(f"Response preview: {analysis_response[:200]}...")
            return None

    except Exception as e:
        print(f"❌ Error in LLM-based reference analysis: {e}")
        return None


def process_llm_reference_analysis(analysis_data: Dict, chunks_list: List[Dict]) -> Tuple[Dict[int, float], Dict]:
    """
    Process LLM analysis results and convert to contribution scores format
    """
    if not analysis_data or 'source_analysis' not in analysis_data:
        return {}, None

    contribution_scores = {}

    # Convert LLM analysis to contribution scores
    for chunk_analysis in analysis_data['source_analysis']:
        chunk_id = chunk_analysis.get('chunk_id')
        confidence_score = chunk_analysis.get('confidence_score', 50)
        was_used = chunk_analysis.get('was_used', True)

        if chunk_id is not None and chunk_id < len(chunks_list):
            # Convert confidence score (0-100) to contribution score (0.0-1.0)
            if was_used:
                score = min(1.0, max(0.0, confidence_score / 100.0))
            else:
                score = min(0.3, confidence_score / 100.0)  # Penalty for unused chunks

            contribution_scores[chunk_id] = score

    # Ensure all chunks have scores (default to low score for unanalyzed chunks)
    for i in range(len(chunks_list)):
        if i not in contribution_scores:
            contribution_scores[i] = 0.2

    # Create analysis summary for frontend
    analysis_summary = {
        'total_chunks': len(chunks_list),
        'analyzed_chunks': len(analysis_data['source_analysis']),
        'high_relevance_count': len(
            [a for a in analysis_data['source_analysis'] if a.get('confidence_score', 0) >= 80]),
        'medium_relevance_count': len(
            [a for a in analysis_data['source_analysis'] if 50 <= a.get('confidence_score', 0) < 80]),
        'low_relevance_count': len([a for a in analysis_data['source_analysis'] if a.get('confidence_score', 0) < 50]),
        'overall_assessment': analysis_data.get('overall_assessment', ''),
        'analysis_method': 'llm_based'
    }

    return contribution_scores, analysis_summary


def calculate_fallback_contribution_scores(chunks_list: List[Dict], final_answer: str) -> Dict[int, float]:
    """Fallback scoring method when semantic model is not available"""
    scores = {}
    clean_answer = clean_text_for_analysis(final_answer)

    # Extract keywords from answer for better matching
    answer_words = set(clean_answer.lower().split()) if clean_answer else set()

    for i, chunk in enumerate(chunks_list):
        chunk_content = clean_text_for_analysis(chunk.get('content', ''))

        if chunk_content and clean_answer:
            # Enhanced scoring based on multiple factors
            base_score = calculate_simple_text_overlap(chunk_content, clean_answer)

            # Keyword matching bonus
            chunk_words = set(chunk_content.lower().split())
            common_words = answer_words.intersection(chunk_words)
            keyword_bonus = len(common_words) / len(answer_words) if answer_words else 0

            # Content type multiplier
            content_type_multiplier = get_content_type_multiplier(chunk.get('content_type', 'text'))

            # Remove position bias - all chunks get equal treatment based on content
            # Only apply slight randomization to avoid ties
            randomization_factor = 0.98 + (
                        hash(chunk.get('source', '') + str(i)) % 100) * 0.0004  # ±2% random variation

            # Combine factors
            final_score = (base_score * 0.6 + keyword_bonus * 0.4) * content_type_multiplier * randomization_factor
            scores[i] = min(1.0, max(0.1, final_score))
        else:
            # Assign varied scores based on source/content characteristics, not position
            source_name = chunk.get('source', 'unknown')
            content_type = chunk.get('content_type', 'text')

            # Base score on content characteristics
            source_hash = hash(source_name) % 100
            type_bonus = {'text_with_title': 0.1, 'table': 0.05, 'text': 0.0}.get(content_type, 0.0)

            base_fallback = 0.2 + (source_hash % 5) * 0.06 + type_bonus  # 0.2 to 0.54 range
            scores[i] = min(0.6, max(0.15, base_fallback))

    # Debug info for fallback scoring
    if scores:
        scores_list = list(scores.values())
        logger.info(f"Fallback scores before normalization - range: {min(scores_list):.3f} to {max(scores_list):.3f}")

        # Log top scoring chunks for debugging
        if chunks_list:
            sorted_scores = sorted([(i, score, chunks_list[i].get('source', 'unknown')) for i, score in scores.items()],
                                   key=lambda x: x[1], reverse=True)
            logger.info(f"Top 3 scoring chunks: {sorted_scores[:3]}")

    normalized_scores = normalize_contribution_scores(scores)

    if normalized_scores:
        normalized_list = list(normalized_scores.values())
        logger.info(
            f"Fallback scores after normalization - range: {min(normalized_list):.3f} to {max(normalized_list):.3f}")

        # Log top scoring chunks after normalization for debugging
        if chunks_list:
            sorted_norm = sorted(
                [(i, score, chunks_list[i].get('source', 'unknown')) for i, score in normalized_scores.items()],
                key=lambda x: x[1], reverse=True)
            logger.info(f"Top 3 normalized chunks: {sorted_norm[:3]}")

    return normalized_scores


def clean_text_for_analysis(text: str) -> str:
    """Clean text for semantic analysis"""
    if not text:
        return ""

    # Remove common prefixes and suffixes
    text = re.sub(r'^(Answer:\s*|AI:\s*|Based on.*?[:,]\s*)', '', text, flags=re.IGNORECASE)
    text = re.sub(r'\s*\[.*?\]\s*$', '', text)  # Remove reference citations at end

    # Clean up whitespace and formatting
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()

    return text


def calculate_keyword_overlap_score(chunk_text: str, answer_text: str, question_text: str) -> float:
    """Calculate keyword overlap score between chunk and answer/question"""
    try:
        # Extract important words (filter out common stop words manually)
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is',
                      'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would',
                      'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'}

        def extract_keywords(text):
            words = re.findall(r'\b\w+\b', text.lower())
            return set(word for word in words if len(word) > 2 and word not in stop_words)

        chunk_keywords = extract_keywords(chunk_text)
        answer_keywords = extract_keywords(answer_text)
        question_keywords = extract_keywords(question_text)

        if not chunk_keywords:
            return 0.0

        # Calculate overlap with answer and question
        answer_overlap = len(chunk_keywords.intersection(answer_keywords)) / len(
            chunk_keywords) if chunk_keywords else 0
        question_overlap = len(chunk_keywords.intersection(question_keywords)) / len(
            chunk_keywords) if chunk_keywords else 0

        return (answer_overlap * 0.7 + question_overlap * 0.3)

    except Exception:
        return 0.2


def calculate_simple_text_overlap(text1: str, text2: str) -> float:
    """Enhanced text overlap calculation for fallback"""
    try:
        # Common stop words to exclude
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is',
                      'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would',
                      'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'it', 'they',
                      'them', 'their', 'there', 'then', 'than', 'when', 'where', 'why', 'how', 'what', 'who', 'which',
                      'whose', 'whom'}

        # Extract meaningful words (length > 2, not stop words)
        def extract_meaningful_words(text):
            words = re.findall(r'\b\w+\b', text.lower())
            return set(word for word in words if len(word) > 2 and word not in stop_words)

        words1 = extract_meaningful_words(text1)
        words2 = extract_meaningful_words(text2)

        if not words1 or not words2:
            return 0.1

        # Calculate overlap metrics
        intersection = words1.intersection(words2)
        union = words1.union(words2)

        # Jaccard similarity (intersection over union)
        jaccard = len(intersection) / len(union) if union else 0

        # Overlap coefficient (intersection over smaller set)
        overlap_coeff = len(intersection) / min(len(words1), len(words2)) if words1 and words2 else 0

        # Combine both metrics
        final_score = (jaccard * 0.6) + (overlap_coeff * 0.4)

        return max(0.05, min(0.95, final_score))

    except:
        return 0.2


def get_content_type_multiplier(content_type: str) -> float:
    """Get multiplier based on content type importance"""
    multipliers = {
        'text': 1.0,
        'text_with_title': 1.2,  # Titles often more important
        'table': 1.1,  # Tables contain structured info
        'image': 0.9,  # Images less directly semantic
        'chart': 1.0,
        'flowchart': 1.0,
        'confusion_matrix': 1.0
    }
    return multipliers.get(content_type, 1.0)


def normalize_contribution_scores(scores: Dict[int, float]) -> Dict[int, float]:
    """Normalize scores to ensure good distribution"""
    if not scores:
        return scores

    score_values = list(scores.values())
    min_score = min(score_values)
    max_score = max(score_values)

    # If all scores are the same, create artificial variation based on content hash (not position)
    if max_score - min_score < 0.01:
        logger.info("Scores too similar, creating content-based differentiation")
        normalized = {}
        chunk_hashes = {}

        # Get chunk info for hash-based variation
        for i in scores.keys():
            # Use index to approximate chunk info when not available directly
            chunk_hashes[i] = hash(str(i) + "content") % 100

        for i, score in scores.items():
            # Create variation based on content hash, not position
            variation = 0.3 + (chunk_hashes[i] % 7) * 0.08  # Gives scores from 0.3 to 0.78
            normalized[i] = min(0.9, max(0.1, variation))
        return normalized

    # Normalize to 0-1 range with some minimum threshold
    normalized = {}
    for i, score in scores.items():
        normalized_score = (score - min_score) / (max_score - min_score)
        # Ensure minimum score of 0.1 and maximum of 1.0, with better spread
        normalized[i] = max(0.1, min(1.0, normalized_score * 0.8 + 0.2))

    return normalized


def add_contribution_scores_to_chunks(chunks_list: List[Dict], contribution_scores: Dict[int, float]) -> List[Dict]:
    """Add contribution scores to chunks metadata (fallback method)"""
    enhanced_chunks = []

    for i, chunk in enumerate(chunks_list):
        enhanced_chunk = chunk.copy()
        enhanced_chunk['contribution_score'] = contribution_scores.get(i, 0.5)
        enhanced_chunk['contribution_rank'] = 0  # Will be filled after sorting
        enhanced_chunks.append(enhanced_chunk)

    # Sort by contribution score and add ranks
    sorted_chunks = sorted(enumerate(enhanced_chunks), key=lambda x: x[1]['contribution_score'], reverse=True)

    for rank, (original_index, chunk) in enumerate(sorted_chunks, 1):
        chunk['contribution_rank'] = rank

    return enhanced_chunks


def add_contribution_scores_to_chunks_llm(chunks_list: List[Dict], contribution_scores: Dict[int, float],
                                          llm_analysis: Dict = None) -> List[Dict]:
    """Add LLM-based contribution scores to chunks metadata"""
    enhanced_chunks = []

    # Create mapping from LLM analysis for additional metadata
    llm_explanations = {}
    if llm_analysis and 'source_analysis' in llm_analysis:
        for chunk_analysis in llm_analysis['source_analysis']:
            chunk_id = chunk_analysis.get('chunk_id')
            if chunk_id is not None:
                llm_explanations[chunk_id] = {
                    'explanation': chunk_analysis.get('contribution_explanation', ''),
                    'relevance_rank': chunk_analysis.get('relevance_rank', 0),
                    'was_used': chunk_analysis.get('was_used', True)
                }

    for i, chunk in enumerate(chunks_list):
        enhanced_chunk = chunk.copy()
        enhanced_chunk['contribution_score'] = contribution_scores.get(i, 0.2)
        enhanced_chunk['contribution_rank'] = 0  # Will be filled after sorting

        # Add LLM-specific metadata
        if i in llm_explanations:
            enhanced_chunk['llm_explanation'] = llm_explanations[i]['explanation']
            enhanced_chunk['llm_relevance_rank'] = llm_explanations[i]['relevance_rank']
            enhanced_chunk['llm_was_used'] = llm_explanations[i]['was_used']
        else:
            enhanced_chunk['llm_explanation'] = 'Not analyzed by LLM'
            enhanced_chunk['llm_relevance_rank'] = 999
            enhanced_chunk['llm_was_used'] = False

        enhanced_chunks.append(enhanced_chunk)

    # Sort by contribution score and add ranks
    sorted_chunks = sorted(enumerate(enhanced_chunks), key=lambda x: x[1]['contribution_score'], reverse=True)

    for rank, (original_index, chunk) in enumerate(sorted_chunks, 1):
        chunk['contribution_rank'] = rank

    return enhanced_chunks


def format_contribution_analysis_for_frontend(enhanced_chunks: List[Dict]) -> Dict[str, Any]:
    """Format contribution analysis results for frontend display (fallback method)"""
    total_chunks = len(enhanced_chunks)

    # Calculate statistics
    scores = [chunk['contribution_score'] for chunk in enhanced_chunks]
    avg_score = sum(scores) / len(scores) if scores else 0
    max_score = max(scores) if scores else 0
    min_score = min(scores) if scores else 0

    # Categorize chunks by contribution level
    high_contribution = [chunk for chunk in enhanced_chunks if chunk['contribution_score'] >= 0.7]
    medium_contribution = [chunk for chunk in enhanced_chunks if 0.3 <= chunk['contribution_score'] < 0.7]
    low_contribution = [chunk for chunk in enhanced_chunks if chunk['contribution_score'] < 0.3]

    analysis_summary = {
        'total_chunks': total_chunks,
        'high_contribution_count': len(high_contribution),
        'medium_contribution_count': len(medium_contribution),
        'low_contribution_count': len(low_contribution),
        'average_score': round(avg_score, 3),
        'max_score': round(max_score, 3),
        'min_score': round(min_score, 3),
        'score_distribution': {
            'high': len(high_contribution) / total_chunks if total_chunks > 0 else 0,
            'medium': len(medium_contribution) / total_chunks if total_chunks > 0 else 0,
            'low': len(low_contribution) / total_chunks if total_chunks > 0 else 0
        },
        'analysis_method': 'fallback'
    }

    return {
        'analysis_summary': analysis_summary,
        'chunks_by_contribution': {
            'high': high_contribution,
            'medium': medium_contribution,
            'low': low_contribution
        }
    }


def format_llm_analysis_for_frontend(enhanced_chunks: List[Dict], analysis_summary: Dict) -> Dict[str, Any]:
    """Format LLM analysis results for frontend display"""
    total_chunks = len(enhanced_chunks)

    # Calculate score statistics
    scores = [chunk['contribution_score'] for chunk in enhanced_chunks]
    avg_score = sum(scores) / len(scores) if scores else 0
    max_score = max(scores) if scores else 0
    min_score = min(scores) if scores else 0

    # Categorize chunks by contribution level
    high_contribution = [chunk for chunk in enhanced_chunks if chunk['contribution_score'] >= 0.8]
    medium_contribution = [chunk for chunk in enhanced_chunks if 0.5 <= chunk['contribution_score'] < 0.8]
    low_contribution = [chunk for chunk in enhanced_chunks if chunk['contribution_score'] < 0.5]

    frontend_analysis = {
        'analysis_summary': {
            **analysis_summary,
            'average_score': round(avg_score, 3),
            'max_score': round(max_score, 3),
            'min_score': round(min_score, 3),
            'score_distribution': {
                'high': len(high_contribution) / total_chunks if total_chunks > 0 else 0,
                'medium': len(medium_contribution) / total_chunks if total_chunks > 0 else 0,
                'low': len(low_contribution) / total_chunks if total_chunks > 0 else 0
            }
        },
        'chunks_by_contribution': {
            'high': high_contribution,
            'medium': medium_contribution,
            'low': low_contribution
        }
    }

    return frontend_analysis


# =============================================================================
# END TWO-STAGE LLM REFERENCE ANALYSIS SYSTEM
# =============================================================================

app = Flask(__name__)

# File Upload Configuration
os.makedirs(env.default_upload_folder, exist_ok=True)
app.config['UPLOAD_FOLDER'] = env.default_upload_folder  # save all files in the default folder
app.config['ALLOWED_EXTENSIONS'] = env.valid_file_extension  # valid file formats
app.config['PROCESSED_FOLDER'] = 'processed/'

""" Temp """
prompt_template_list = init_interface.load_prompt_template()
model_dict, basemodel_list = init_interface.load_model_template()

""" Inititalization """
# Retrieval of Default Configuration
config_dir = os.path.join(env.default_dir, env.default_config_dir)
config = pd.read_json(config_dir, typ="series")

# Retriveval of Preprocessing Configuration
chunk_mode = 'chunk-1'
embedding_mode = 'embedding-3'  # Changed to use BGE-M3 model
chunk_method, embedding_method = init_interface.load_preprocessing_config_chi(chunk_mode,
                                                                              embedding_mode)  # For the Chinese version
# chunk_method, embedding_method = init_interface.load_preprocessing_config(chunk_mode, embedding_mode) # For the English version

# *** DIMENSION CHECK AND FIX ***
# Check the actual embedding dimension
print("🔍 检查embedding模型维度...")
sample_text = "测试文本"
try:
    sample_embedding = embedding_method.embed_documents([sample_text])
    actual_dim = len(sample_embedding[0])
    print(f"✅ 实际embedding维度: {actual_dim}")
except Exception as e:
    print(f"❌ 无法检测embedding维度: {e}")
    actual_dim = 384  # 默认fallback

# Auto-select appropriate schema based on embedding dimension
if actual_dim == 384:
    vsschema_uid = 'vsschema-9'  # 384维schema
    print(f"✅ 使用384维schema: {vsschema_uid}")
elif actual_dim == 768:
    vsschema_uid = 'vsschema-8'  # 768维schema (最新的带content_type字段)
    print(f"✅ 使用768维schema: {vsschema_uid}")
elif actual_dim == 1024:
    vsschema_uid = 'vsschema-10'  # 1024维schema for BGE-M3
    print(f"✅ 使用1024维schema: {vsschema_uid}")
else:
    print(f"⚠️  未知的embedding维度: {actual_dim}，使用默认384维schema")
    vsschema_uid = 'vsschema-9'

# Retrieval of System Prompt Parameters
prompt_dir = os.path.join(env.default_dir, env.default_prompt_dir)
system_prompt_para = pd.read_json(prompt_dir, typ="series")

# Retrieval of Vector Store Configuration
vs_dir = os.path.join(env.vs_dir_root, "milvus" + env.vs_config_suffix)
vs_config = pd.read_json(vs_dir, typ="series")

dataset_list = vectorstore_module_newui.init_vsdb(vs_config)
print("load dataset")
print(*dataset_list, sep='\n')

# *** Load schema config after dimension check ***
schema_config_dir = env.vs_dir_root + vsschema_uid + env.vsschema_config_fmt
schema_config = pd.read_json(schema_config_dir, typ='series')

# Verify schema dimension matches embedding dimension
schema_vector_dim = schema_config['field_1']['dim']
print(f"🔍 Schema向量维度: {schema_vector_dim}, Embedding维度: {actual_dim}")
if schema_vector_dim != actual_dim:
    print(f"❌ 维度不匹配！Schema期望{schema_vector_dim}维，但embedding是{actual_dim}维")
    print(f"🔧 建议:")
    if actual_dim == 384:
        print("   - 使用 vsschema-9 (384维)")
    elif actual_dim == 768:
        print("   - 使用 vsschema-8 或其他768维schema")
    print("   - 或者更换embedding模型以匹配schema维度")
else:
    print(f"✅ 维度匹配成功！")

# Retrieval RAG Configuration
search_method = 'search-1'
search_config = pd.read_json(env.search_config_dir + \
                             search_method + \
                             env.search_config_suffix, typ="series")

# Retrieval of User Information
""" *** Refine Later >>> Build simple user authentication function """
# raw_uid = 0o02
# # uid_digit = config["uid_digit"]
# # user_id = f"%0{uid_digit}d" %raw_uid

user_id = dt.datetime.now().strftime(datetime_userid_format)

# User Authentication
# user_fullform = env.user_prefix + str(user_id)
user_fullform = env.user_prefix + user_id

user_dir = os.path.join(env.user_dir_root, user_fullform)

# Retrieval of User Profile
# user_profile = os.path.join(user_dir, user_fullform + env.user_prof_suffix)

# Retrieval of User Chat History
user_history = os.path.join(user_dir, user_fullform + env.user_chat_suffix)
print("user_history: ", user_history)
chat_history = {"dir": None,
                "df": None,
                "langchain": None}
print("chat_history: ", chat_history)

chat_history["langchain"] = ChatMessageHistory()
print("chat_history(langchain)", chat_history["langchain"])

chat_history["dir"] = user_history
if not os.path.exists(chat_history["dir"]):
    chat_history["df"] = pd.DataFrame()
    chat_history["langchain"].add_user_message("")
    chat_history["langchain"].add_ai_message("")

else:
    df = pd.read_json(chat_history["dir"], orient="index")
    # print("chat_history(dir): ", chat_history["dir"])

    chat_history["df"] = df
    # print("chat_history(df): ", chat_history["df"])

    # Convert Chat History to langchain Format
    for q_msg, a_msg in zip(df["q_msg"].to_list(),
                            df["a_msg"].to_list()):
        chat_history["langchain"].add_user_message(q_msg)
        chat_history["langchain"].add_ai_message(a_msg)
        # print("chat_history:", chat_history)
        # print("chat_history[langchain]:", chat_history["langchain"])

    # print("chat_history(langchain)", chat_history["langchain"])
    chat_history["langchain"] = chat_history["langchain"].messages
    # print("chat_history(langchain)(messages)", chat_history["langchain"])

print("Chat History at Init: ", chat_history)

# Init Collection
loaded_dataset = None
loaded_dataset_name = None
loaded_files = []

# ***** Retrival of stroed collections and documents
#  {'dataset_name': 'Dataset_Woody_Test',
#  'document_list': ['kyototxt', 'alicetxt']}]
dataset_list = vectorstore_module_newui.init_vsdb(vs_config)

# initate llm pipeline
# torch.set_default_tensor_type(torch.cuda.HalfTensor)
mixtral_config = r"model/mixtral/mixtral-1_config.json"
# pipeline_starttime = datetime.now()
# mixtral_pipeline = mixtral_module.init_mixtral_piepeline(pd.read_json(mixtral_config, typ='series'))
# mixtral_pipeline = mixtral_module_org.init_mixtral_piepeline_2()
llama_config = r"model/llama/llama+rag-1_config.json"
# llama_pipeline = llama_module.init_llama_piepeline(pd.read_json(llama_config, typ='series'))
pipeline_endtime = datetime.now()
# print(f"Pipeline Loading Time = {pipeline_endtime-pipeline_starttime}")

# print(mixtral_pipeline)
""" Chatbot """


@app.route('/chat', methods=['GET'])
def index():
    # return render_template('index.html')
    return render_template('index_json_database_new_ui_24.html')


selected_dataset = None  # dataset storing files


@app.route('/selected-dataset', methods=['POST'])
def point_selected_dataset():
    global selected_dataset
    dataset = request.get_json()
    selected_dataset = dataset['selectedDataset']
    print(f'Selected Dataset: <{selected_dataset}>')
    return 'Received Dataset Selection'


selected_instrcut = 'role-default'


@app.route('/select-instruct', methods=['POST'])
def select_instruct():
    global selected_instrcut
    data = request.json

    selected_instrcut = data['selectedInstruct']
    print(f'Selected instruct option <{selected_instrcut}>')
    return jsonify({'message': 'Received instruct option: ' + selected_instrcut})


from datetime import datetime
import fitz  # PyMuPDF
from typing import List, Tuple, Dict
import numpy as np
from sklearn.cluster import DBSCAN
from pydantic import BaseModel
import requests
from flask import jsonify, request
from langchain_community.document_loaders import PyMuPDFLoader, DirectoryLoader
import tiktoken
from uuid import UUID
import os
from datetime import datetime
from flask import jsonify, request
import fitz  # PyMuPDF
from typing import List, Tuple, Dict
import numpy as np
from sklearn.cluster import DBSCAN
from langchain_community.document_loaders import PyMuPDFLoader, DirectoryLoader
from langchain.schema import Document
import preprocessing
import vectorstore_module_newui
import requests
from pydantic import BaseModel
from typing import Optional
from datetime import datetime
import pdfplumber
from PIL import Image as PILImage
import io
import pdb
from openai import OpenAI
from img2table.document import Image
from IPython.display import display_html
# Use PaddleOCR
from img2table.ocr import PaddleOCR

ocr = PaddleOCR(lang="ch")


# Use AzureOCR
# from img2table.ocr import AzureOCR
# from dotenv import load_dotenv
# import os

# load_dotenv()
# # Get the endpoint and subscription key from .env file
# endpoint = os.getenv('AZURE_OCR_ENDPOINT')
# subscription_key = os.getenv('AZURE_OCR_SUBSCRIPTION_KEY')

# # Use the variables in your OCR setup
# ocr = AzureOCR(endpoint=endpoint, subscription_key=subscription_key)

class ImageDescriptionPromptResultResponse(BaseModel):
    entry_id: UUID
    image_name: Optional[str] = None
    prompt_result: Optional[str] = None
    number_of_input_tokens: Optional[int] = None
    number_of_output_tokens: Optional[int] = None

    class Config:
        from_attributes = True


class ImageFileBase64(BaseModel):
    image_file_name: Optional[str] = None
    image_file_bytes_base64str: Optional[str] = None
    image_file_type: Optional[str] = None

    class Config:
        from_attributes = True


class OCRResultResponse(BaseModel):
    ocr_requestid: UUID
    ocr_name: Optional[str]
    ocr_image_results: Optional[list[ImageFileBase64]]
    ocr_text_results: Optional[list[str]]

    class Config:
        from_attributes = True


client = OpenAI(api_key='ollama', base_url="http://*************:30291/v1")


def png_image_to_base64_data_uri(file_path):
    with open(file_path, "rb") as img_file:
        base64_data = base64.b64encode(img_file.read()).decode('utf-8')
        return f"data:image/png;base64,{base64_data}"


def truncate_text(text, max_length=65535):
    if isinstance(text, list):
        text = '\n'.join([' | '.join(map(str, row)) for row in text])
    if len(text) > max_length:
        return text[:max_length], True
    return text, False


def detect_images(page, file_name, folder_path="image"):
    """
    Detect and process images from a PDF page
    Uses InternVL for OCR processing
    """
    images_extraction = []
    image_descriptions = []
    number_of_tokens = 0
    number_of_input_tokens = 0
    number_of_output_tokens = 0

    # Create folder if it doesn't exist
    os.makedirs(f"{folder_path}/{file_name}", exist_ok=True)

    def clamp(value, min_val, max_val):
        """Helper function to clamp values within bounds"""
        return max(min_val, min(value, max_val))

    for i, image in enumerate(page.images):
        # Get page dimensions for boundary checking
        page_width = page.width
        page_height = page.height

        # Calculate initial coordinates
        x0 = image['x0']
        invertedy0 = page_height - image['y1']
        invertedy1 = page_height - image['y0']

        # Clamp coordinates to page boundaries
        x0 = clamp(x0, 0, page_width)
        x1 = clamp(image['x1'], 0, page_width)
        y0 = clamp(invertedy0, 0, page_height)
        y1 = clamp(invertedy1, 0, page_height)

        # Create bbox with clamped coordinates
        bbox = (x0, y0, x1, y1)

        # Ensure bbox dimensions are valid (width and height > 0)
        if bbox[2] <= bbox[0] or bbox[3] <= bbox[1]:
            print(f"Skipping invalid bbox for image {i}: {bbox}")
            continue

        try:
            # Extract and save the image
            cropped_image = page.crop(bbox)
            image_object = cropped_image.to_image(resolution=200)
            image_name = f"image-new-{page.page_number}-{i}"
            image_file = f"{folder_path}/{file_name}/{image_name}.png"
            image_object.save(image_file)

            print(f"Detected image: {image_name}")

            # Use InternVL for OCR processing
            try:
                # Convert image to base64 for API call
                image_base64 = png_image_to_base64_data_uri(image_file)

                # Prepare the message for InternVL
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "Please extract and describe all text content visible in this image. If there are charts, tables, or diagrams, describe their content and any visible text or numbers."
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": image_base64
                                }
                            }
                        ]
                    }
                ]

                # Call InternVL API
                response = client.chat.completions.create(
                    model="OpenGVLab/InternVL3-78B-AWQ",
                    messages=messages,
                    stream=False,
                    # max_tokens=1000,
                    # temperature=0.1
                )

                # Extract the OCR text from response
                ocr_text = response.choices[0].message.content

                # Count tokens
                from transformers import AutoTokenizer
                tokenizer = AutoTokenizer.from_pretrained("hkunlp/instructor-large")
                tokens = tokenizer.encode(ocr_text)
                current_tokens = len(tokens)
                number_of_tokens += current_tokens
                number_of_input_tokens += response.usage.prompt_tokens if hasattr(response, 'usage') else 0
                number_of_output_tokens += response.usage.completion_tokens if hasattr(response,
                                                                                       'usage') else current_tokens

                print(f"InternVL OCR result for {image_name}: {len(ocr_text)} characters, {current_tokens} tokens")
                print(f"OCR Text: {ocr_text[:200]}...")

                image_descriptions.append(ocr_text)
                images_extraction.append({"type": "image", "bbox": bbox, "id": (page.page_number, i)})

            except Exception as e:
                print(f"Error during InternVL OCR processing for image {image_name}: {str(e)}")
                # Fallback to simple text
                fallback_text = "this is image content for image"
                image_descriptions.append(fallback_text)
                images_extraction.append({"type": "image", "bbox": bbox, "id": (page.page_number, i)})

        except Exception as e:
            print(f"Error processing image {i}: {str(e)}")
            continue

    return images_extraction, image_descriptions, number_of_input_tokens, number_of_output_tokens, number_of_tokens


def clean_nested_table(table):
    clean_table_text = ""
    for row in table:
        for element in row:
            if element is not None and element != "":
                element = element.replace("\n", "")
                clean_table_text += element + " "
    return clean_table_text.strip()


def parse_html_table_to_text(html_content, show_separator=True, separator_style='auto'):
    """
    将HTML表格转换为格式化的纯文本
    保持表格结构但移除HTML标签

    Args:
        html_content: HTML表格字符串
        show_separator: 是否显示标题行后的分隔线
        separator_style: 分隔线样式 ('auto', 'fixed', 'minimal')
                        - 'auto': 根据列宽自动调整长度
                        - 'fixed': 固定长度分隔线（每列30字符）
                        - 'minimal': 最小分隔线（每列3个短横线）

    Returns:
        str: 格式化的纯文本表格
    """
    from html.parser import HTMLParser
    import re

    class TableParser(HTMLParser):
        def __init__(self):
            super().__init__()
            self.rows = []
            self.current_row = []
            self.current_cell = ""
            self.in_td = False
            self.in_th = False

        def handle_starttag(self, tag, attrs):
            if tag == 'tr':
                self.current_row = []
            elif tag in ['td', 'th']:
                self.in_td = True
                self.in_th = (tag == 'th')
                self.current_cell = ""

        def handle_endtag(self, tag):
            if tag == 'tr':
                if self.current_row:
                    self.rows.append(self.current_row)
            elif tag in ['td', 'th']:
                self.in_td = False
                self.in_th = False
                # 清理单元格内容
                cleaned_cell = re.sub(r'\s+', ' ', self.current_cell.strip())
                self.current_row.append(cleaned_cell)
                self.current_cell = ""

        def handle_data(self, data):
            if self.in_td or self.in_th:
                self.current_cell += data

    try:
        # 如果输入已经是纯文本，直接返回
        if not html_content.strip().startswith('<'):
            return html_content

        parser = TableParser()
        parser.feed(html_content)

        if not parser.rows:
            return html_content

        # 转换为格式化文本
        formatted_text = ""

        # 计算每列的最大宽度（用于对齐）
        max_widths = []
        for row in parser.rows:
            for i, cell in enumerate(row):
                if i >= len(max_widths):
                    max_widths.append(0)
                # 考虑中英文字符宽度差异
                cell_width = len(cell) + len([c for c in cell if ord(c) > 127])
                max_widths[i] = max(max_widths[i], cell_width)

        # 生成格式化表格
        for i, row in enumerate(parser.rows):
            row_text = ""
            for j, cell in enumerate(row):
                if j < len(max_widths):
                    # 计算需要的填充空格
                    cell_display_width = len(cell) + len([c for c in cell if ord(c) > 127])
                    padding = max_widths[j] - cell_display_width + 2
                    row_text += cell + " " * max(padding, 1)
                else:
                    row_text += cell + "  "

            formatted_text += row_text.rstrip() + "\n"

            # 在标题行后添加分隔线（可选）
            if show_separator and i == 0 and len(parser.rows) > 1:
                separator = ""
                for j, cell in enumerate(row):
                    if j < len(max_widths):
                        if separator_style == 'minimal':
                            separator += "--- "
                        elif separator_style == 'fixed':
                            separator += "-" * min(30, max_widths[j]) + "  "
                        else:  # 'auto'
                            separator += "-" * (max_widths[j] + 2)
                    else:
                        if separator_style == 'minimal':
                            separator += "--- "
                        elif separator_style == 'fixed':
                            separator += "-" * min(30, len(cell)) + "  "
                        else:  # 'auto'
                            separator += "-" * (len(cell) + 2)
                formatted_text += separator.rstrip() + "\n"

        return formatted_text.strip()

    except Exception as e:
        print(f"解析HTML表格时出错: {str(e)}")
        # 如果解析失败，尝试简单的标签移除
        try:
            clean_text = re.sub(r'<[^>]+>', '', html_content)
            clean_text = re.sub(r'\s+', ' ', clean_text)
            return clean_text.strip()
        except:
            return html_content


def convert_html_table_to_markdown(html_content):
    """
    将HTML表格转换为Markdown格式的表格

    Args:
        html_content: HTML表格字符串

    Returns:
        str: Markdown格式的表格
    """
    from html.parser import HTMLParser
    import re

    class MarkdownTableParser(HTMLParser):
        def __init__(self):
            super().__init__()
            self.rows = []
            self.current_row = []
            self.current_cell = ""
            self.in_td = False
            self.in_th = False

        def handle_starttag(self, tag, attrs):
            if tag == 'tr':
                self.current_row = []
            elif tag in ['td', 'th']:
                self.in_td = True
                self.in_th = (tag == 'th')
                self.current_cell = ""

        def handle_endtag(self, tag):
            if tag == 'tr':
                if self.current_row:
                    self.rows.append(self.current_row)
            elif tag in ['td', 'th']:
                self.in_td = False
                self.in_th = False
                cleaned_cell = re.sub(r'\s+', ' ', self.current_cell.strip())
                # 转义Markdown特殊字符
                cleaned_cell = cleaned_cell.replace('|', '\\|')
                self.current_row.append(cleaned_cell)
                self.current_cell = ""

        def handle_data(self, data):
            if self.in_td or self.in_th:
                self.current_cell += data

    try:
        if not html_content.strip().startswith('<'):
            return html_content

        parser = MarkdownTableParser()
        parser.feed(html_content)

        if not parser.rows:
            return html_content

        markdown_text = ""

        for i, row in enumerate(parser.rows):
            # 创建表格行
            markdown_text += "| " + " | ".join(row) + " |\n"

            # 在第一行后添加分隔行
            if i == 0:
                separator = "|" + "|".join([" --- " for _ in row]) + "|\n"
                markdown_text += separator

        return markdown_text.strip()

    except Exception as e:
        print(f"转换为Markdown表格时出错: {str(e)}")
        return html_content


def detect_tables(page, file_name, folder_path="table"):
    # prev_page_num=-1
    table_extraction = []
    tables = page.find_tables()
    # if tables:
    # os.makedirs(f"{folder_path}/{file_name}", exist_ok=True)
    # prev_page_num=page.page_number
    # tables_text = [ t.extract(x_tolerance=2, y_tolerance=0) for t in tables ]
    # page_image=page.to_image(resolution=200)
    # current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    # table_name = f"table-{page.page_number}-{current_time}"

    for i, t in enumerate(tables):
        width, height = t.bbox[2] - t.bbox[0], t.bbox[3] - t.bbox[1]
        new_bbox = (
            t.bbox[0] - 0.1 * width,
            t.bbox[1] - 0.1 * height,
            t.bbox[2] + 0.1 * width,
            t.bbox[3] + 0.1 * height
        )
        tables_text = t.extract(x_tolerance=2, y_tolerance=0)
        tables_text = clean_nested_table(tables_text)
        # fill_color = (0, 0, 0, 0)
        # stroke_color = (0, 0, 255)
        # page_image.draw_rect(bbox, fill=fill_color, stroke=stroke_color, stroke_width=5)

        # print(tables_text[i])
        table_extraction.append({"text": tables_text, "bbox": new_bbox, "id": (page.page_number, i), "type": "table"})

    # if prev_page_num!=-1:
    #     page_image.save(f"{folder_path}/{file_name}/{table_name}.png")
    return table_extraction


# def detect_images(page, file_name, folder_path="image"):
#     images = []
#     image_descriptions = []
#     os.makedirs(folder_path, exist_ok=True)
#     for img in page.get_images(full=True):
#         xref = img[0]
#         try:
#             base_image = page.parent.extract_image(xref)
#             if base_image:
#                 pix = fitz.Pixmap(page.parent, xref)
#                 current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
#                 image_name = f"{file_name}-image-{xref}_{current_time}"
#                 image_path = os.path.join(folder_path, f"{image_name}.png")
#                 pix.save(image_path)
#                 post_data = {
#                     'image name': image_name ,
#                     'image format': '.png',
#                     'use default prompt': False,
#                     'prompt': 'Give me a short description on the image'
#                 }
#                 post_image = [('image', (f'{image_name }', pix.tobytes()))]
#                 print("call api")
#                 res = requests.get('http://*************:58887/api/image-description/result', data=post_data,
#                                    files=post_image)
#                 print(res.status_code)
#                 if res.status_code >= 300:
#                     print(f"Fail to transfer image on page {page.number + 1} to text")
#                     return
#                 structured_res = ImageDescriptionPromptResultResponse(**res.json())
#                 text = structured_res.prompt_result
#                 encoding = tiktoken.get_encoding("cl100k_base")
#                 tokens = encoding.encode(text)
#                 print("number of tokens:",len(tokens))

#                 # print(structured_res.image_name)
#                 # print(text)

#                 image_descriptions.append(text)

#                 bbox = (pix.x, pix.y, pix.x + pix.width, pix.y + pix.height)
#                 images.append({"type": "image", "bbox": bbox})
#         except Exception as e:
#             print(f"Error processing image on page {page.number + 1}: {str(e)}")
#     return images, image_descriptions

# def detect_rects(
#     page,
#     filename,
#     padding1=0,
#     padding2=0,
#     padding3=0,
#     padding4=0,
#     folder_path="rec",
#     graphics=None,
# ):
#     """Detect and join rectangles of neighboring vector graphics.
#     parameter:
#         page: pdf current page
#         filename: the name of processed file
#         padding1: the percentage value of padding on the left side of rect (0-1)
#         padding2: the percentage value of padding at top (0-1)
#         padding3: the percentage value of padding on the right side of rect (0-1)
#         padding4: the percentage value of padding at bottom (0-1)
#         folder_path: where to save images of detected tables and charts
#         graphics: the vector graphics of the page
#     return: It returns a list of joined rectangles - which for instance can be used as the
#             "clip" parameter of a Pixmap.
#     """
#     delta = 3

#     def are_neighbors(r1, r2):
#         """Detect whether r1, r2 are "neighbors".

#         Neighbors are defined as:
#         The minimum distance between points of r1 and points of r2 is not
#         larger than delta.

#         This check supports empty rect-likes and thus also lines.
#         """
#         if (
#             (
#                 r2.x0 - delta <= r1.x0 <= r2.x1 + delta
#                 or r2.x0 - delta <= r1.x1 <= r2.x1 + delta
#             )
#             and (
#                 r2.y0 - delta <= r1.y0 <= r2.y1 + delta
#                 or r2.y0 - delta <= r1.y1 <= r2.y1 + delta
#             )
#             or (
#                 r1.x0 - delta <= r2.x0 <= r1.x1 + delta
#                 or r1.x0 - delta <= r2.x1 <= r1.x1 + delta
#             )
#             and (
#                 r1.y0 - delta <= r2.y0 <= r1.y1 + delta
#                 or r1.y0 - delta <= r2.y1 <= r1.y1 + delta
#             )
#         ):
#             return True
#         return False

#     # we exclude graphics not contained in reasonable page margins
#     parea = page.rect + (-36, -36, 36, 36)

#     if graphics is None:
#         graphics = page.get_drawings()
#     # exclude graphics not contained inside margins
#     paths = [
#         p
#         for p in page.get_drawings()
#         if parea.x0 <= p["rect"].x0 <= p["rect"].x1 <= parea.x1
#         and parea.y0 <= p["rect"].y0 <= p["rect"].y1 <= parea.y1
#     ]

#     # list of all vector graphic rectangles
#     prects = sorted([p["rect"] for p in paths], key=lambda r: (r.y1, r.x0))
#     new_rects = []  # the final list of the joined rectangles

#     # -------------------------------------------------------------------------
#     # The strategy is to identify and join all rects that are neighbors
#     # -------------------------------------------------------------------------
#     while prects:  # the algorithm will empty this list
#         r = prects[0]  # first rectangle
#         repeat = True
#         while repeat:
#             repeat = False
#             for i in range(len(prects) - 1, 0, -1):  # back to front
#                 if are_neighbors(prects[i], r):
#                     r |= prects[i].tl  # join in to first rect
#                     r |= prects[i].br
#                     del prects[i]  # delete this rect
#                     repeat = True

#         prects[0] = +r
#         # move first item over to result list
#         new_rects.append(prects.pop(0))
#         prects = sorted(list(set(prects)), key=lambda r: (r.y1, r.x0))

#     new_rects = sorted(list(set(new_rects)), key=lambda r: (r.y1, r.x0))
#     filter_rects = (
#         []
#     )  # add padding to the rects to make the image not lose important info
#     for r in new_rects:
#         if r.width > 20 and r.height > 20:
#             r.x0 = r.x0 - padding1 * (r.x1 - r.x0)
#             r.y0 = r.y0 - padding2 * (r.y1 - r.y0)
#             r.x1 = r.x1 + padding3 * (r.x1 - r.x0)
#             r.y1 = r.y1 + padding4 * (r.y1 - r.y0)
#             filter_rects.append(r)

#     current_time = datetime.now().strftime("%Y-%m-%d_%H-%M")
#     os.makedirs(f"{folder_path}/{filename}", exist_ok=True)  # save the found recs in one folder
#     for i, r in enumerate(filter_rects):
#         pix = page.get_pixmap(dpi=150, clip=r)
#         image_name=("graphic-%03i-%02i" % (page.number, i))
#         bbox = (pix.x, pix.y, pix.x + pix.width, pix.y + pix.height)
#         pix.save(f"{folder_path}/{filename}/{image_name}_{current_time}.png") #save image

#         post_data = {
#                     "name": image_name,
#                     "ocr image format": ".png",
#                     "language":'ch'
#             }
#         post_image = [("image", (f"{image_name}", pix.tobytes()))]
#         res = requests.get(
#                     "http://*************:58880/api/ocr/upload-image/result",
#                     data=post_data,
#                     files=post_image,
#                 )
#         print(res.status_code)
#         if res.status_code >= 300:
#             print(f"Fail to transfer image on page {page.number + 1} to text")
#             return
#         print(image_name)
#         print(bbox)
#         requested_ocr_result=OCRResultResponse(**res.json())
#         # print(requested_ocr_result.ocr_text_results)
#         text=requested_ocr_result.ocr_text_results[0]
#         # print(text)

#     return filter_rects

# def detect_tables(page):
#     tables = []
#     words = page.get_text("words")
#     lines = page.get_drawings()
#     rects = [l for l in lines if l["type"] == "rect"]
#
#     for rect in rects:
#         rect_words = [w for w in words if fitz.Rect(w[:4]).intersects(rect["rect"])]
#         if len(rect_words) > 4:  # Assume it's a table if it contains more than 4 words
#             tables.append({"type": "table", "bbox": rect["rect"]})
#
#     return tables


# def detect_charts(page):
#     charts = []
#     paths = page.get_drawings()
#     lines = [p for p in paths if p["type"] == "line"]
#
#     if len(lines) > 10:  # Assume it's a chart if there are many lines
#         # Use DBSCAN to cluster lines and identify potential charts
#         line_points = np.array([(l["rect"][0], l["rect"][1]) for l in lines])
#         clustering = DBSCAN(eps=20, min_samples=5).fit(line_points)
#
#         unique_labels = set(clustering.labels_)
#         for label in unique_labels:
#             if label != -1:  # -1 is noise
#                 cluster_points = line_points[clustering.labels_ == label]
#                 x_min, y_min = np.min(cluster_points, axis=0)
#                 x_max, y_max = np.max(cluster_points, axis=0)
#                 charts.append({"type": "chart", "bbox": (x_min, y_min, x_max, y_max)})
#
#     return charts


# def detect_flowcharts(page):
#     flowcharts = []
#     paths = page.get_drawings()
#     shapes = [p for p in paths if p["type"] in ["rect", "circle"]]
#     lines = [p for p in paths if p["type"] == "line"]
#
#     if len(shapes) > 5 and len(lines) > 5:  # Assume it's a flowchart if there are shapes and connecting lines
#         all_points = [(s["rect"][0], s["rect"][1]) for s in shapes] + [(l["rect"][0], l["rect"][1]) for l in lines]
#         points_array = np.array(all_points)
#         x_min, y_min = np.min(points_array, axis=0)
#         x_max, y_max = np.max(points_array, axis=0)
#         flowcharts.append({"type": "flowchart", "bbox": (x_min, y_min, x_max, y_max)})
#
#     return flowcharts
#
#
# def detect_confusion_matrices(page):
#     matrices = []
#     words = page.get_text("words")
#     numbers = [w for w in words if w[4].replace(".", "").isdigit()]
#
#     if len(numbers) > 4:  # Assume it's a confusion matrix if there are many numbers in a grid-like structure
#         numbers_array = np.array([(float(n[0]), float(n[1])) for n in numbers])
#         clustering = DBSCAN(eps=20, min_samples=4).fit(numbers_array)
#
#         if len(set(clustering.labels_)) > 1:  # If we have clusters, it might be a matrix
#             x_min, y_min = np.min(numbers_array, axis=0)
#             x_max, y_max = np.max(numbers_array, axis=0)
#             matrices.append({"type": "confusion_matrix", "bbox": (x_min, y_min, x_max, y_max)})
#
#     return matrices


def detect_drawings_pdf(page, images_bbox_list, table_bbox_list, words_bbox_list, text_with_title_bbox_list=None,
                        chart_bbox_list=None, flowchart_bbox_list=None):
    """
    改进的PDF绘图函数，支持更多内容类型

    Args:
        page: PDF页面对象
        images_bbox_list: 图像边界框列表
        table_bbox_list: 表格边界框列表
        words_bbox_list: 普通文本边界框列表
        text_with_title_bbox_list: 标题文本边界框列表
        chart_bbox_list: 图表边界框列表
        flowchart_bbox_list: 流程图边界框列表

    Returns:
        bool: 是否成功绘制任何标注
    """
    flag = False

    # 定义颜色映射
    color_mapping = {
        'image': (1, 0, 0),  # 红色
        'table': (0, 0, 1),  # 蓝色
        'text': (0, 1, 0),  # 绿色
        'text_with_title': (0, 0.8, 0),  # 深绿色
        'chart': (1, 0.5, 0),  # 橙色
        'flowchart': (0.5, 0, 1)  # 紫色
    }

    # 绘制图像边界框
    if images_bbox_list:
        for i_bbox in images_bbox_list:
            page.draw_rect(fitz.Rect(i_bbox), color=color_mapping['image'], fill=None, width=2)
        flag = True

    # 绘制表格边界框
    if table_bbox_list:
        for t_bbox in table_bbox_list:
            page.draw_rect(fitz.Rect(t_bbox), color=color_mapping['table'], fill=None, width=2)
        flag = True

    # 绘制普通文本边界框
    if words_bbox_list:
        for w_bbox in words_bbox_list:
            page.draw_rect(fitz.Rect(w_bbox), color=color_mapping['text'], fill=None, width=2)
        flag = True

    # 绘制标题文本边界框
    if text_with_title_bbox_list:
        for tt_bbox in text_with_title_bbox_list:
            page.draw_rect(fitz.Rect(tt_bbox), color=color_mapping['text_with_title'], fill=None, width=2)
        flag = True

    # 绘制图表边界框
    if chart_bbox_list:
        for c_bbox in chart_bbox_list:
            page.draw_rect(fitz.Rect(c_bbox), color=color_mapping['chart'], fill=None, width=2)
        flag = True

    # 绘制流程图边界框
    if flowchart_bbox_list:
        for f_bbox in flowchart_bbox_list:
            page.draw_rect(fitz.Rect(f_bbox), color=color_mapping['flowchart'], fill=None, width=2)
        flag = True

    return flag


def bbox_intersects(bbox1, bbox2):
    """ Check if two bounding boxes intersect """
    x1, y1, x2, y2 = bbox1
    a1, b1, a2, b2 = bbox2
    return not (x2 < a1 or x1 > a2 or y2 < b1 or y1 > b2)


def get_element_description(element_type: str, element: Dict, image_description: str = None) -> str:
    if element_type == 'image':
        return f"{image_description}"
    elif element_type == 'table':
        return f"{element['text']}"
    # elif element_type == 'chart':
    #     return f"Chart detected at coordinates: {element['bbox']}"
    # elif element_type == 'flowchart':
    #     return f"Flowchart detected at coordinates: {element['bbox']}"
    # elif element_type == 'confusion_matrix':
    #     return f"Confusion matrix detected at coordinates: {element['bbox']}"
    else:
        return f"Unknown element type: {element_type}"


def chunk_pdf_advanced(pdf_path: str, file_dataset: str, chunk_size: int = 800, chunk_overlap: int = 20) -> List[
    Tuple[str, Dict]]:
    chunks = []
    doc = fitz.open(pdf_path)
    DEFAULT_BBOX = (10000, 10000, -1, -1)

    doc_pdfplumber = pdfplumber.open(pdf_path)
    element_counters = {"image": 0, "table": 0, "chart": 0, "flowchart": 0, "confusion_matrix": 0}
    file_name = os.path.basename(pdf_path).split('.')[0]
    num_of_input_tokens = 0
    num_of_output_tokens = 0
    total_num_of_tokens = 0

    current_line_number = 1
    current_page_number = 1
    start_time = datetime.now()
    for page_index in range(len(doc)):
        page = doc[page_index]
        page_padplumber = doc_pdfplumber.pages[page_index]  # pageindex+1=page_number

        # detect images
        images, image_descriptions, input_tokens, output_tokens, total_tokens = detect_images(page_padplumber,
                                                                                              file_name)
        image_bbox_list = [element["bbox"] for element in images]
        total_num_of_tokens += total_tokens
        num_of_input_tokens += input_tokens
        num_of_output_tokens += output_tokens

        # detect tables
        tables = detect_tables(page_padplumber, file_name)
        tables_bbox_list = [element["bbox"] for element in tables]
        page_element_counters = {"image": 0, "table": 0, "chart": 0, "flowchart": 0, "confusion_matrix": 0}

        words = page.get_text("words")
        words_dict_list = [{"bbox": word[:4], "text": word[4], "type": "word"} for word in words]
        components = sorted(images + tables + words_dict_list, key=lambda c: (c["bbox"][3], c["bbox"][0]))
        words_bbox_list = []

        current_chunk = ""
        current_metadata = {
            "start_page_num": current_page_number,
            "end_page_num": current_page_number,
            "start_line_num": current_line_number,
            "end_line_num": current_line_number,
            "contains_image": False,
            "contains_table": False,
            "contains_chart": False,
            "contains_flowchart": False,
            "contains_confusion_matrix": False,
            "elements": [],
            "bbox": DEFAULT_BBOX,
            "content_type": "text"
        }
        x_left, y_left, x_right, y_right = DEFAULT_BBOX

        last_y = words[0][3] if words else 0

        for index, c in enumerate(components):
            c_type = c["type"]
            if c_type == "word":
                ignore = False
                for image_bbox in image_bbox_list:
                    if bbox_intersects(c["bbox"], image_bbox):
                        ignore = True
                        break
                if not ignore:
                    for table_bbox in tables_bbox_list:
                        if bbox_intersects(c["bbox"], table_bbox):
                            ignore = True
                            break
                if ignore:
                    continue

                word_text = c["text"] + " "
                if abs(c["bbox"][3] - last_y) > 5:  # New line if y-coordinate differs by more than 5
                    current_line_number += 1
                    last_y = c["bbox"][3]

                current_chunk += word_text
                current_metadata["end_line_num"] = current_line_number
                x_left, y_left, x_right, y_right = min(x_left, c["bbox"][0]), min(y_left, c["bbox"][1]), max(x_right,
                                                                                                             c["bbox"][
                                                                                                                 2]), max(
                    y_right, c["bbox"][3])
                current_metadata["bbox"] = (x_left, y_left, x_right, y_right)
                if len(current_chunk) >= chunk_size:
                    current_metadata["end_page_num"] = current_page_number
                    words_bbox_list.append(current_metadata["bbox"])

                    # Check if the chunk matches the specific pattern
                    chunk_content = current_chunk.strip()
                    if re.match(
                            r'^此文件只供內部參考 \d+ 不可作複印、分發或上載於任何社交媒體平台\s*$|^「AIA」 、 「本公司」 或 「我們」 \(於百慕達註冊成立之有限公司\) 。 是指友邦保險 \(國際\) 有限公司\s*$',
                            chunk_content):
                        chunk_content = ""

                    chunks.append((chunk_content, current_metadata))
                    x_left, y_left, x_right, y_right = DEFAULT_BBOX
                    # Reset current chunk and metadata

                    current_chunk = " ".join(
                        [word for word in current_chunk.strip().rsplit(' ', chunk_overlap + 1)[-1 - chunk_overlap:-1]])
                    current_metadata = {
                        "start_page_num": current_page_number,
                        "end_page_num": current_page_number,
                        "start_line_num": current_line_number,
                        "end_line_num": current_line_number,
                        "content_type": "text",
                        "bbox": DEFAULT_BBOX,
                        "elements": []
                    }
            else:  # component belongs to the elements (images or tables)
                if current_chunk:
                    current_metadata["end_page_num"] = current_page_number
                    words_bbox_list.append(current_metadata["bbox"])

                    # Check if the chunk matches the specific pattern
                    chunk_content = current_chunk.strip()
                    if re.match(
                            r'^此文件只供內部參考 \d+ 不可作複印、分發或上載於任何社交媒體平台\s*$|^「AIA」 、 「本公司」 或 「我們」 \(於百慕達註冊成立之有限公司\) 。 是指友邦保險 \(國際\) 有限公司\s*$',
                            chunk_content):
                        chunk_content = ""

                    chunks.append((chunk_content, current_metadata))
                    x_left, y_left, x_right, y_right = DEFAULT_BBOX

                element_counters[c_type] += 1
                page_element_counters[c_type] += 1

                if c_type == 'image':
                    print("description_all", image_descriptions[page_element_counters[c_type] - 1])
                    description = image_descriptions[page_element_counters[c_type] - 1]
                    print("description", description)
                elif c_type == 'table':
                    description = '\n'.join([' | '.join(row) for row in c['text']])
                else:
                    description = None

                element_description = get_element_description(c_type, c, description)
                element_metadata = {
                    "start_page_num": current_page_number,
                    "end_page_num": current_page_number,
                    "start_line_num": current_line_number,
                    "end_line_num": current_line_number,
                    "content_type": c_type,
                    "elements": [c],
                    "bbox": (
                        min(current_metadata["bbox"][0], c["bbox"][0]), min(current_metadata["bbox"][1], c["bbox"][1]),
                        max(current_metadata["bbox"][2], c["bbox"][2]), max(current_metadata["bbox"][3], c["bbox"][3]))
                }
                chunks.append((current_chunk.strip() + element_description, element_metadata))
                x_left, y_left, x_right, y_right = DEFAULT_BBOX
                # Reset current chunk and metadata
                current_chunk = ""
                current_metadata = {
                    "start_page_num": current_page_number,
                    "end_page_num": current_page_number,
                    "start_line_num": current_line_number,
                    "end_line_num": current_line_number,
                    "content_type": "text",
                    "elements": [],
                    "bbox": DEFAULT_BBOX
                }

        if current_chunk:
            current_metadata["end_page_num"] = current_page_number
            words_bbox_list.append(current_metadata["bbox"])

            # Check if the chunk matches the specific pattern
            chunk_content = current_chunk.strip()
            if re.match(
                    r'^此文件只供內部參考 \d+ 不可作複印、分發或上載於任何社交媒體平台\s*$|^「AIA」 、 「本公司」 或 「我們」 \(於百慕達註冊成立之有限公司\) 。 是指友邦保險 \(國際\) 有限公司\s*$',
                    chunk_content):
                chunk_content = ""

            chunks.append((chunk_content, current_metadata))
            x_left, y_left, x_right, y_right = DEFAULT_BBOX

        current_page_number += 1
        current_line_number = 1

    os.makedirs(f"saved_pdf/{file_dataset}", exist_ok=True)
    output_file_name = f"{os.path.splitext(os.path.basename(pdf_path))[0]}.pdf"
    doc.save(f"saved_pdf/{file_dataset}/{output_file_name}")

    print("total number of tokens (image):", total_num_of_tokens)
    print("number of input tokens (image):", num_of_input_tokens)
    print("number of output tokens (image):", num_of_output_tokens)
    end_time = datetime.now()
    print("Time used: ", end_time - start_time)
    return chunks, element_counters


def merge_short_text_chunks(chunks_with_metadata, min_chunk_size=100, max_chunk_size=800):
    """
    合并短的相邻文本块，特别是title和text类型的块

    Args:
        chunks_with_metadata: 原始的文本块和元数据列表
        min_chunk_size: 最小块大小，小于此大小的块会被考虑合并
        max_chunk_size: 最大块大小，合并后不超过此大小

    Returns:
        合并后的文本块和元数据列表
    """
    if not chunks_with_metadata:
        return chunks_with_metadata

    merged_chunks = []
    current_merged_text = ""
    current_merged_metadata = None

    for i, (chunk_text, metadata) in enumerate(chunks_with_metadata):
        content_type = metadata.get('content_type', 'text')

        # 对于表格和图像类型，不进行合并，直接添加
        if content_type in ['table', 'image', 'chart', 'flowchart', 'confusion_matrix']:
            # 如果之前有积累的文本，先保存
            if current_merged_text.strip():
                merged_chunks.append((current_merged_text.strip(), current_merged_metadata))
                current_merged_text = ""
                current_merged_metadata = None

            # 添加当前的非文本块
            merged_chunks.append((chunk_text, metadata))
            continue

        # 对于文本类型（包括title和text），考虑合并
        if content_type in ['text', 'title', 'heading']:
            # 如果当前块很短，或者累积文本还不够长，继续合并
            should_merge = (
                    len(chunk_text.strip()) < min_chunk_size or  # 当前块很短
                    len(current_merged_text) < min_chunk_size or  # 累积文本还不够长
                    (len(current_merged_text + " " + chunk_text) < max_chunk_size and  # 合并后不会太长
                     current_merged_metadata and
                     current_merged_metadata.get('start_page_num') == metadata.get('start_page_num'))  # 在同一页
            )

            if should_merge and current_merged_text:
                # 合并文本
                if content_type == 'title' and current_merged_text:
                    current_merged_text += "\n\n" + chunk_text  # 标题前加换行
                else:
                    current_merged_text += " " + chunk_text

                # 更新元数据
                current_merged_metadata['end_page_num'] = metadata.get('end_page_num',
                                                                       current_merged_metadata['end_page_num'])
                current_merged_metadata['end_line_num'] = metadata.get('end_line_num',
                                                                       current_merged_metadata['end_line_num'])

                # 如果包含标题，更新内容类型
                if content_type == 'title':
                    current_merged_metadata['content_type'] = 'text_with_title'

                print(
                    f"DEBUG Merge: 合并块 - 类型: {content_type}, 当前长度: {len(current_merged_text)}, 添加内容: '{chunk_text[:50]}...'")

            else:
                # 如果之前有积累的文本，先保存
                if current_merged_text.strip():
                    merged_chunks.append((current_merged_text.strip(), current_merged_metadata))
                    print(
                        f"DEBUG Merge: 保存合并块 - 最终长度: {len(current_merged_text.strip())}, 类型: {current_merged_metadata.get('content_type')}")

                # 开始新的合并
                current_merged_text = chunk_text
                current_merged_metadata = metadata.copy()
                print(f"DEBUG Merge: 开始新合并 - 类型: {content_type}, 初始长度: {len(chunk_text)}")
        else:
            # 其他类型，不合并
            if current_merged_text.strip():
                merged_chunks.append((current_merged_text.strip(), current_merged_metadata))
                current_merged_text = ""
                current_merged_metadata = None

            merged_chunks.append((chunk_text, metadata))

    # 处理最后剩余的文本
    if current_merged_text.strip():
        merged_chunks.append((current_merged_text.strip(), current_merged_metadata))
        print(
            f"DEBUG Merge: 保存最后合并块 - 最终长度: {len(current_merged_text.strip())}, 类型: {current_merged_metadata.get('content_type') if current_merged_metadata else 'unknown'}")

    print(f"文本块合并完成: {len(chunks_with_metadata)} -> {len(merged_chunks)} 个块")
    return merged_chunks


def add_chunk_overlap(chunks_with_metadata, overlap_words=20, overlap_chars=100):
    """
    在相邻的文本块之间添加重叠部分，保持上下文连续性

    Args:
        chunks_with_metadata: 文本块和元数据列表
        overlap_words: 重叠的单词数量（优先使用）
        overlap_chars: 重叠的字符数量（作为备选）

    Returns:
        添加重叠后的文本块和元数据列表
    """
    if not chunks_with_metadata or len(chunks_with_metadata) <= 1:
        return chunks_with_metadata

    overlapped_chunks = []

    for i, (chunk_text, metadata) in enumerate(chunks_with_metadata):
        content_type = metadata.get('content_type', 'text')

        # 只对文本类型的块添加重叠
        if content_type in ['text', 'title', 'heading', 'text_with_title']:
            current_text = chunk_text

            # 如果不是第一个块，从前一个块添加重叠内容
            if i > 0:
                prev_chunk_text, prev_metadata = chunks_with_metadata[i - 1]
                prev_content_type = prev_metadata.get('content_type', 'text')

                # 只从文本类型的前一个块获取重叠内容
                if prev_content_type in ['text', 'title', 'heading', 'text_with_title']:
                    overlap_text = get_overlap_text(prev_chunk_text, overlap_words, overlap_chars)
                    if overlap_text:
                        current_text = overlap_text + " " + current_text
                        print(f"DEBUG Overlap: 第 {i + 1} 个块添加重叠内容: '{overlap_text[:50]}...'")

                        # 更新元数据中的起始位置信息
                        updated_metadata = metadata.copy()
                        if prev_metadata.get('end_line_num'):
                            # 适当调整起始行号，体现重叠
                            overlap_lines = max(1, len(overlap_text.split('\n')))
                            updated_metadata['start_line_num'] = max(1,
                                                                     prev_metadata.get('end_line_num',
                                                                                       1) - overlap_lines + 1)

                        overlapped_chunks.append((current_text, updated_metadata))
                    else:
                        overlapped_chunks.append((chunk_text, metadata))
                else:
                    # 前一个块不是文本类型，不添加重叠
                    overlapped_chunks.append((chunk_text, metadata))
            else:
                # 第一个块，不需要重叠
                overlapped_chunks.append((chunk_text, metadata))
        else:
            # 非文本类型的块（表格、图像等），不添加重叠
            overlapped_chunks.append((chunk_text, metadata))

    print(f"重叠处理完成: 处理了 {len(chunks_with_metadata)} 个块")
    return overlapped_chunks


def get_overlap_text(text, overlap_words=20, overlap_chars=100):
    """
    从文本末尾获取重叠内容

    Args:
        text: 原始文本
        overlap_words: 重叠的单词数量
        overlap_chars: 重叠的字符数量

    Returns:
        重叠的文本内容
    """
    if not text or not text.strip():
        return ""

    text = text.strip()

    # 优先按单词分割（适用于英文）
    words = text.split()
    if len(words) >= overlap_words:
        overlap_text = ' '.join(words[-overlap_words:])
        return overlap_text
    elif len(words) > 0:
        # 如果单词数不够，使用所有单词
        overlap_text = ' '.join(words)
        return overlap_text

    # 如果按单词分割效果不好，按字符分割（适用于中文）
    if len(text) >= overlap_chars:
        # 从句子边界开始截取，避免截断句子
        overlap_text = text[-overlap_chars:]
        # 尝试找到句子的开始
        sentence_markers = ['。', '！', '？', '.', '!', '?', '\n']
        for marker in sentence_markers:
            marker_pos = overlap_text.find(marker)
            if marker_pos > 0:
                overlap_text = overlap_text[marker_pos + 1:].strip()
                break
        return overlap_text
    else:
        # 文本太短，返回整个文本作为重叠
        return text


def process_precomputed_layout(json_path: str, markdown_path: str = None) -> List[Tuple[str, Dict]]:
    """
    处理预计算的文档布局文件（JSON和Markdown）

    Args:
        json_path: JSON文件路径，包含文档的结构化数据
        markdown_path: Markdown文件路径（可选，主要用作参考）

    Returns:
        List[Tuple[str, Dict]]: 与chunk_pdf_advanced相同格式的输出
    """
    print(f"正在处理预计算的布局文件: {json_path}")

    try:
        # 读取JSON文件
        with open(json_path, 'r', encoding='utf-8') as f:
            precomputed_data = json.load(f)

        print(f"JSON文件读取成功，数据类型: {type(precomputed_data)}")

        # 检查数据是否为空
        if not precomputed_data:
            print("JSON文件为空")
            return None, None

        chunks_with_metadata = []

        # 检查JSON数据格式
        if isinstance(precomputed_data, list):
            print(f"检测到格式1: 直接chunks列表，包含 {len(precomputed_data)} 个项目")
            # 格式1: 直接是chunks列表
            for i, item in enumerate(precomputed_data):
                if isinstance(item, dict) and 'chunk_text' in item and 'metadata' in item:
                    chunk_text = item['chunk_text']
                    metadata = item['metadata']

                    # 跳过空文本块
                    if not chunk_text or chunk_text.strip() == "":
                        print(f"跳过第 {i + 1} 个空文本块")
                        continue

                    # 确保元数据包含必要的字段
                    required_metadata = {
                        'start_page_num': metadata.get('start_page_num', 1),
                        'end_page_num': metadata.get('end_page_num', 1),
                        'start_line_num': metadata.get('start_line_num', 1),
                        'end_line_num': metadata.get('end_line_num', 1),
                        'content_type': metadata.get('content_type', 'text'),
                        'bbox': metadata.get('bbox', (0, 0, 0, 0)),
                        'source': metadata.get('source', os.path.basename(json_path).replace('.json', ''))
                    }

                    chunks_with_metadata.append((chunk_text, required_metadata))
                    print(f"处理第 {i + 1} 个文本块: {len(chunk_text)} 字符, 类型: {required_metadata['content_type']}")
                else:
                    print(f"跳过第 {i + 1} 个项目: 缺少必要的字段 (chunk_text 或 metadata)")

        elif isinstance(precomputed_data, dict):
            print("检测到字典格式，检查具体结构...")
            # 格式2: 可能包含页面结构的字典格式
            if 'pages' in precomputed_data:
                print(f"检测到格式2: 页面结构格式，包含 {len(precomputed_data['pages'])} 页")
                for page_data in precomputed_data['pages']:
                    page_num = page_data.get('page_number', 1)

                    if 'chunks' in page_data:
                        for i, chunk_data in enumerate(page_data['chunks']):
                            chunk_text = chunk_data.get('text', '')

                            # 跳过空文本块
                            if not chunk_text or chunk_text.strip() == "":
                                print(f"跳过页面 {page_num} 第 {i + 1} 个空文本块")
                                continue

                            metadata = {
                                'start_page_num': page_num,
                                'end_page_num': page_num,
                                'start_line_num': chunk_data.get('start_line', 1),
                                'end_line_num': chunk_data.get('end_line', 1),
                                'content_type': chunk_data.get('type', 'text'),
                                'bbox': chunk_data.get('bbox', (0, 0, 0, 0)),
                                'source': os.path.basename(json_path).replace('.json', '')
                            }

                            chunks_with_metadata.append((chunk_text, metadata))
                            print(f"处理页面 {page_num} 第 {i + 1} 个文本块: {len(chunk_text)} 字符")

            # 格式3: 直接包含文档级别的chunks
            elif 'chunks' in precomputed_data:
                print(f"检测到格式3: 文档级别chunks，包含 {len(precomputed_data['chunks'])} 个项目")
                for i, chunk_data in enumerate(precomputed_data['chunks']):
                    chunk_text = chunk_data.get('text', '')

                    # 跳过空文本块
                    if not chunk_text or chunk_text.strip() == "":
                        print(f"跳过第 {i + 1} 个空文本块")
                        continue

                    metadata = {
                        'start_page_num': chunk_data.get('page_number', 1),
                        'end_page_num': chunk_data.get('page_number', 1),
                        'start_line_num': chunk_data.get('start_line', 1),
                        'end_line_num': chunk_data.get('end_line', 1),
                        'content_type': chunk_data.get('type', 'text'),
                        'bbox': chunk_data.get('bbox', (0, 0, 0, 0)),
                        'source': os.path.basename(json_path).replace('.json', '')
                    }

                    chunks_with_metadata.append((chunk_text, metadata))
                    print(f"处理第 {i + 1} 个文本块: {len(chunk_text)} 字符")

            # 格式4: Miner工具生成的格式（包含pdf_info）
            elif 'pdf_info' in precomputed_data:
                print(f"检测到格式4: Miner工具格式，包含 {len(precomputed_data['pdf_info'])} 页")

                for page_idx, page_info in enumerate(precomputed_data['pdf_info']):
                    page_num = page_info.get('page_idx', page_idx) + 1  # page_idx通常从0开始
                    print(f"处理第 {page_num} 页...")

                    # 统计当前页面的元素
                    page_stats = {
                        'preproc_blocks': len(page_info.get('preproc_blocks', [])),
                        'para_blocks': len(page_info.get('para_blocks', [])),
                        'images': len(page_info.get('images', [])),
                        'tables': len(page_info.get('tables', [])),
                        'text_found': 0
                    }
                    print(f"页面统计: {page_stats}")

                    # 用于去重的集合，存储已处理的内容hash
                    processed_content_hashes = set()

                    # 处理所有可能包含文本的块（改进的去重逻辑）
                    text_blocks_to_process = []

                    # 优先处理preproc_blocks（通常是最完整的）
                    if 'preproc_blocks' in page_info:
                        for block in page_info['preproc_blocks']:
                            text_blocks_to_process.append(('preproc', block))

                    # 只有当preproc_blocks不存在或为空时才处理para_blocks
                    elif 'para_blocks' in page_info:
                        for block in page_info['para_blocks']:
                            text_blocks_to_process.append(('para', block))

                    # 添加images中的图像（确保类型正确）
                    if 'images' in page_info:
                        for image in page_info['images']:
                            text_blocks_to_process.append(('image', image))

                    # 添加tables中的表格（独立处理，因为结构不同）
                    if 'tables' in page_info:
                        for table in page_info['tables']:
                            text_blocks_to_process.append(('table', table))

                    # 处理所有文本块
                    for block_idx, (source_type, block) in enumerate(text_blocks_to_process):
                        content_type = block.get('type', 'text')
                        bbox = block.get('bbox', [0, 0, 0, 0])
                        original_type_from_json = content_type

                        # 改进的文本提取，避免重复
                        block_text = extract_text_from_block_improved(block)

                        # 根据实际内容和source_type正确设置content_type
                        if source_type == 'table' or content_type == 'table':
                            content_type = 'table'
                            # 尝试提取表格的结构化文本
                            if not block_text and 'cells' in block:
                                table_text = ""
                                for cell in block.get('cells', []):
                                    cell_text = extract_text_from_block_improved(cell)
                                    if cell_text:
                                        table_text += cell_text + " | "
                                if table_text:
                                    block_text = table_text
                        elif content_type == 'image' or block_text == "image content":
                            content_type = 'image'

                            # 处理图像OCR - 从嵌套结构中查找image_path
                            image_path = ''

                            # 方法1: 直接在block中查找image_path
                            if 'image_path' in block:
                                image_path = block['image_path']

                            # 方法2: 在lines/spans结构中查找image_path
                            elif 'lines' in block:
                                for line in block['lines']:
                                    if 'spans' in line:
                                        for span in line['spans']:
                                            if span.get('type') == 'image' and 'image_path' in span:
                                                image_path = span['image_path']
                                                break
                                    if image_path:
                                        break

                            # 方法3: 在spans中直接查找（如果没有lines包装）
                            elif 'spans' in block:
                                for span in block['spans']:
                                    if span.get('type') == 'image' and 'image_path' in span:
                                        image_path = span['image_path']
                                        break

                            # 方法4: 在blocks/lines/spans嵌套结构中查找
                            elif 'blocks' in block:
                                for sub_block in block['blocks']:
                                    if 'lines' in sub_block:
                                        for line in sub_block['lines']:
                                            if 'spans' in line:
                                                for span in line['spans']:
                                                    if span.get('type') == 'image' and 'image_path' in span:
                                                        image_path = span['image_path']
                                                        break
                                            if image_path:
                                                break

                            print(
                                f"DEBUG: 查找图像路径结果 - image_path: '{image_path}', block keys: {list(block.keys())}")

                            if image_path:
                                upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
                                # 获取文档名称用于构建子文件夹路径
                                document_name = os.path.basename(json_path).replace('.json', '')
                                ocr_text, should_include = process_image_with_ocr(image_path, upload_folder,
                                                                                  min_text_length=10,
                                                                                  document_name=document_name)

                                if should_include and ocr_text:
                                    # 用OCR文字替换"image content"
                                    block_text = ocr_text
                                    print(f"✅ 图像 {image_path} OCR成功，文字长度: {len(ocr_text)}")
                                else:
                                    # 跳过此图像块
                                    print(f"⚠️  跳过图像 {image_path} - 文字不足或处理失败")
                                    continue
                            else:
                                print(f"⚠️  图像块缺少image_path信息，跳过")
                                continue
                        elif block_text and block_text.strip().startswith('<html>'):
                            content_type = 'table'

                        # 调试打印，显示原始类型和最终确定的类型
                        print(
                            f"DEBUG Layout Processing: Page {page_num}, Block {block_idx + 1} - Original Type: '{original_type_from_json}', Final Type: '{content_type}', Content: '{block_text[:200]}...'")

                        # 去重检查：计算内容hash
                        if block_text.strip():
                            # 检查是否为模板性重复内容
                            def is_template_content(text):
                                """检查是否为模板性内容"""
                                import re
                                text_clean = text.strip()
                                if len(text_clean) < 5:  # 过短的内容可能是模板
                                    return True
                                template_patterns = [
                                    r'^Note:?\s*$',
                                    r'^Remarks?:?\s*$',
                                    r'^Notation\s+圖例\s*$',
                                    r'^\d+\s*NOTES?\s+TO\s+PURCHASERS?\s*',
                                    r'^重要事項\s*$',
                                    r'^免責聲明\s*$'
                                ]
                                for pattern in template_patterns:
                                    if re.match(pattern, text_clean, re.IGNORECASE):
                                        return True
                                return False

                            content_hash = hash(block_text.strip())
                            if content_hash in processed_content_hashes:
                                print(f"⚠️  第 {page_num} 页第 {block_idx + 1} 个块 ({source_type}) 重复内容，跳过")
                                continue

                            # 对于模板性内容，增加页面信息到hash中避免过度去重
                            if is_template_content(block_text.strip()):
                                content_hash = hash(f"{block_text.strip()}_{page_num}")
                                print(
                                    f"🔄 第 {page_num} 页第 {block_idx + 1} 个块 ({source_type}) 检测为模板内容，使用页面特定hash")

                            processed_content_hashes.add(content_hash)

                            metadata = {
                                'start_page_num': page_num,
                                'end_page_num': page_num,
                                'start_line_num': block_idx + 1,
                                'end_line_num': block_idx + 1,
                                'content_type': content_type,
                                'bbox': tuple(bbox) if isinstance(bbox, list) else bbox,
                                'source': os.path.basename(json_path).replace('.json', '')
                            }

                            chunks_with_metadata.append((block_text.strip(), metadata))
                            page_stats['text_found'] += 1
                            print(
                                f"✅ 第 {page_num} 页第 {block_idx + 1} 个块 ({source_type}): {len(block_text.strip())} 字符, 类型: {content_type}")
                        else:
                            print(
                                f"⚠️  第 {page_num} 页第 {block_idx + 1} 个块 ({source_type}) 无文本内容, 类型: {content_type}")

                    print(f"第 {page_num} 页完成，提取到 {page_stats['text_found']} 个文本块")

            else:
                print("无法识别的字典格式，缺少 'pages'、'chunks' 或 'pdf_info' 键")
                print(f"可用的顶级键: {list(precomputed_data.keys())}")
                return None, None

        else:
            print(f"不支持的数据格式: {type(precomputed_data)}")
            return None, None

        if len(chunks_with_metadata) == 0:
            print("警告: 没有找到有效的文本块")
            return None, None

        print(f"成功处理预计算布局文件，共提取 {len(chunks_with_metadata)} 个文本块")

        # 合并短的文本块
        chunks_with_metadata = merge_short_text_chunks(chunks_with_metadata)

        # 注意：重叠功能现在在主流程中统一处理，这里不再添加重叠

        # 返回格式：(chunks_with_metadata, element_counters)
        # 计算元素计数器
        element_counters = {"image": 0, "table": 0, "chart": 0, "flowchart": 0, "confusion_matrix": 0}
        for _, metadata in chunks_with_metadata:
            content_type = metadata.get('content_type', 'text')
            if content_type in element_counters:
                element_counters[content_type] += 1

        print(f"元素统计: {element_counters}")
        return chunks_with_metadata, element_counters

    except FileNotFoundError:
        print(f"错误: 找不到文件 {json_path}")
        return None, None
    except json.JSONDecodeError as e:
        print(f"错误: JSON格式无效 - {str(e)}")
        return None, None
    except Exception as e:
        print(f"处理预计算布局文件时出错: {str(e)}")
        print(f"将回退到原始PDF处理方式")
        return None, None


def extract_text_from_block_improved(block):
    """改进的文本提取函数，避免重复提取，正确处理图像和表格"""
    text_content = ""

    # 检查块的类型
    block_type = block.get('type', 'text')

    # 处理图像类型
    if block_type == 'image':
        return "image content"

    # 处理表格类型 - 优先返回HTML内容
    if block_type == 'table':
        # 首先检查直接的HTML字段
        html_fields = ['html', 'HTML', 'table_html', 'content_html']
        for field in html_fields:
            if field in block and block[field]:
                print(f"🔍 找到表格HTML内容 (直接字段 {field}): {len(block[field])} 字符")
                # 使用新的HTML解析函数转换为格式化文本（不显示长分隔线）
                return parse_html_table_to_text(block[field], show_separator=False)

        # 检查blocks结构中的HTML
        if 'blocks' in block:
            for sub_block in block['blocks']:
                if 'lines' in sub_block:
                    for line in sub_block['lines']:
                        if 'spans' in line:
                            for span in line['spans']:
                                # 检查span中的HTML内容 - 这是关键修复
                                if 'html' in span and span['html']:
                                    print(f"🎯 找到表格HTML内容 (spans中): {len(span['html'])} 字符")
                                    return parse_html_table_to_text(span['html'], show_separator=False)
                                # 检查其他可能的HTML字段
                                for field in html_fields:
                                    if field in span and span[field]:
                                        print(f"🎯 找到表格HTML内容 (spans中的 {field}): {len(span[field])} 字符")
                                        return parse_html_table_to_text(span[field], show_separator=False)

        # 如果有lines结构但没有blocks包装
        if 'lines' in block:
            for line in block['lines']:
                if 'spans' in line:
                    for span in line['spans']:
                        # 检查span中的HTML内容
                        if 'html' in span and span['html']:
                            print(f"🎯 找到表格HTML内容 (直接lines/spans): {len(span['html'])} 字符")
                            return parse_html_table_to_text(span['html'], show_separator=False)
                        # 检查其他HTML字段
                        for field in html_fields:
                            if field in span and span[field]:
                                print(f"🎯 找到表格HTML内容 (直接lines/spans的 {field}): {len(span[field])} 字符")
                                return parse_html_table_to_text(span[field], show_separator=False)

        # 如果没有HTML，尝试其他表格内容
        if 'content' in block:
            print(f"📋 使用表格content字段: {len(block['content'])} 字符")
            return parse_html_table_to_text(block['content'], show_separator=False)
        if 'text' in block:
            print(f"📋 使用表格text字段: {len(block['text'])} 字符")
            return parse_html_table_to_text(block['text'], show_separator=False)

        print("⚠️  表格块未找到HTML或文本内容")

    # 优先级策略：按照数据结构的完整性选择提取方法

    # 方法1: 如果有blocks结构（用于处理嵌套结构）
    if 'blocks' in block:
        for sub_block in block['blocks']:
            if 'lines' in sub_block:
                for line in sub_block['lines']:
                    if 'spans' in line:
                        for span in line['spans']:
                            # 处理各种包含文本内容的span类型
                            span_type = span.get('type', '')
                            if span_type in ['text', 'inline_equation', 'formula', 'math', 'equation']:
                                if 'text' in span:
                                    text_content += span['text'] + " "
                                elif 'content' in span:
                                    text_content += span['content'] + " "
        if text_content.strip():  # 如果成功提取到内容，直接返回
            return text_content.strip()

    # 方法2: 如果有lines和spans结构，优先使用（最详细）
    if 'lines' in block:
        for line in block['lines']:
            if 'spans' in line:
                for span in line['spans']:
                    # 处理各种包含文本内容的span类型
                    span_type = span.get('type', '')
                    if span_type in ['text', 'inline_equation', 'formula', 'math', 'equation']:
                        if 'text' in span:
                            text_content += span['text'] + " "
                        elif 'content' in span:
                            text_content += span['content'] + " "
        if text_content.strip():  # 如果成功提取到内容，直接返回
            return text_content.strip()

    # 方法3: 如果有spans结构（直接spans）
    if 'spans' in block:
        for span in block['spans']:
            # 处理各种包含文本内容的span类型
            span_type = span.get('type', '')
            if span_type in ['text', 'inline_equation', 'formula', 'math', 'equation']:
                if 'text' in span:
                    text_content += span['text'] + " "
                elif 'content' in span:
                    text_content += span['content'] + " "
        if text_content.strip():  # 如果成功提取到内容，直接返回
            return text_content.strip()

    # 方法4: 最后尝试直接文本字段
    if 'text' in block:
        text_content = block['text']
    elif 'content' in block:
        text_content = block['content']

    return text_content.strip()


def extract_text_from_block(block):
    """从块中深度提取文本内容的辅助函数"""
    # 为了保持兼容性，调用改进版本
    return extract_text_from_block_improved(block)


def process_markdown_fallback(markdown_path: str, source_filename: str) -> List[Tuple[str, Dict]]:
    """
    当JSON文件不存在或无效时，尝试处理Markdown文件作为备选方案

    Args:
        markdown_path: Markdown文件路径
        source_filename: 源文件名

    Returns:
        List[Tuple[str, Dict]]: 处理后的文本块和元数据
    """
    print(f"尝试处理Markdown文件作为备选方案: {markdown_path}")

    try:
        with open(markdown_path, 'r', encoding='utf-8') as f:
            markdown_content = f.read()

        # 简单的Markdown分块策略
        # 按照Markdown标题和段落进行分割
        chunks_with_metadata = []

        # 分割成段落
        paragraphs = markdown_content.split('\n\n')

        for i, paragraph in enumerate(paragraphs):
            if paragraph.strip():  # 跳过空段落
                # 创建基本的元数据
                metadata = {
                    'start_page_num': 1,
                    'end_page_num': 1,
                    'start_line_num': i + 1,
                    'end_line_num': i + 1,
                    'content_type': 'text',
                    'bbox': (0, 0, 0, 0),
                    'source': os.path.basename(source_filename)
                }

                # 检测内容类型
                if paragraph.strip().startswith('#'):
                    metadata['content_type'] = 'heading'
                elif '|' in paragraph and paragraph.count('|') > 2:
                    metadata['content_type'] = 'table'
                elif paragraph.strip().startswith('!['):
                    metadata['content_type'] = 'image'

                chunks_with_metadata.append((paragraph.strip(), metadata))

        # 计算元素计数器
        element_counters = {"image": 0, "table": 0, "chart": 0, "flowchart": 0, "confusion_matrix": 0}
        for _, metadata in chunks_with_metadata:
            content_type = metadata.get('content_type', 'text')
            if content_type in element_counters:
                element_counters[content_type] += 1

        print(f"成功处理Markdown文件，共提取 {len(chunks_with_metadata)} 个文本块")
        # 注意：重叠功能现在在主流程中统一处理，这里不再添加重叠
        return chunks_with_metadata, element_counters

    except Exception as e:
        print(f"处理Markdown文件时出错: {str(e)}")
        return None, None


@app.route('/upload', methods=['POST'])
def upload_file():
    global vs_config
    global selected_dataset
    global schema_config
    global vs_df  # log file for vector store
    global user_id

    selected_dataset = request.form.get('selectedDataset', None)

    if not selected_dataset:
        selected_dataset = env.default_dataset_name
        print(f'* No Selected Dataset Name : Default Name <{selected_dataset}> is Used')

    # Create Collection
    created = vectorstore_module_newui.create_vscollection(
        vs_config,
        selected_dataset,
        schema_config
    )
    graph_store = graphstore_module_newui(selected_dataset)

    print(f'... Uploading the File into the Selected Dataset <{selected_dataset}>')
    uploaded_files = request.files.getlist('file')
    print("uploaded_files: ", uploaded_files)

    if not uploaded_files:
        print('Error: No Uploading Files Found!')
        return jsonify({'status': 'error', 'message': 'No files found for upload'})

    from transformers import AutoTokenizer
    tokenizer = AutoTokenizer.from_pretrained("hkunlp/instructor-large")
    total_tokens = 0

    for file in uploaded_files:
        if file:
            filename = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
            file.save(filename)
            print("saved file")

            # 检查是否存在预计算的布局文件
            base_name = os.path.splitext(file.filename)[0]  # 移除扩展名
            json_path = os.path.join(app.config['UPLOAD_FOLDER'], base_name + '.json')
            markdown_path = os.path.join(app.config['UPLOAD_FOLDER'], base_name + '.md')

            # 初始化变量
            chunks_with_metadata = None
            element_counters = None

            # 优先使用预计算的布局文件
            if os.path.exists(json_path):
                print(f"找到预计算布局文件: {json_path}")
                chunks_with_metadata, element_counters = process_precomputed_layout(json_path,
                                                                                    markdown_path if os.path.exists(
                                                                                        markdown_path) else None)

                if chunks_with_metadata is not None and len(chunks_with_metadata) > 0:
                    print(f"成功使用预计算布局，提取了 {len(chunks_with_metadata)} 个文本块")
                else:
                    print("预计算布局处理失败，回退到原始处理方式")
                    chunks_with_metadata = None

            # 如果预计算布局不可用，尝试Markdown文件
            if chunks_with_metadata is None and os.path.exists(markdown_path):
                print(f"找到Markdown文件，尝试使用: {markdown_path}")
                chunks_with_metadata, element_counters = process_markdown_fallback(markdown_path, file.filename)

                if chunks_with_metadata is not None:
                    print("成功使用Markdown文件")
                else:
                    print("Markdown文件处理失败，回退到原始处理方式")

            # 如果以上都不可用，使用原始处理方式
            if chunks_with_metadata is None:
                print("使用原始处理方式")
                if filename.lower().endswith(".pdf"):
                    chunks_with_metadata, element_counters = chunk_pdf_advanced(filename, selected_dataset)
                else:
                    loader = DirectoryLoader(app.config['UPLOAD_FOLDER'], show_progress=True, use_multithreading=True)
                    document = loader.load()
                    print("loaded other type document")
                    if not document:
                        print('Warning: Cannot Find Document in Upload Folder')
                    chunked_content = preprocessing.chunk_document(document, chunk_method)
                    # 转换为统一格式
                    chunks_with_metadata = []
                    for i, chunk in enumerate(chunked_content):
                        metadata = {
                            'start_page_num': 1,
                            'end_page_num': 1,
                            'start_line_num': i + 1,
                            'end_line_num': i + 1,
                            'content_type': 'text',
                            'bbox': (0, 0, 0, 0),
                            'source': os.path.basename(file.filename)
                        }
                        chunks_with_metadata.append((chunk.page_content, metadata))
                    element_counters = {"image": 0, "table": 0, "chart": 0, "flowchart": 0, "confusion_matrix": 0}

            # 检查是否有有效的文本块
            if not chunks_with_metadata or len(chunks_with_metadata) == 0:
                print(f'Warning: 文件 {file.filename} 处理后没有有效的文本块，跳过此文件')
                continue

            # 【关键修复】在这里统一应用重叠功能
            print(f"应用重叠功能前: {len(chunks_with_metadata)} 个文本块")
            chunks_with_metadata = add_chunk_overlap(chunks_with_metadata,
                                                     overlap_words=CHUNK_OVERLAP_WORDS,
                                                     overlap_chars=CHUNK_OVERLAP_CHARS)
            print(f"应用重叠功能后: {len(chunks_with_metadata)} 个文本块")

            # 现在统一处理文本清理
            processed_chunks_with_metadata = []
            for chunk_text, metadata in chunks_with_metadata:
                # 对文本块进行清理
                cleaned_chunk = chunk_text
                cleaned_chunk = re.sub(r'All words in the image: 「擎識定期壽險計劃」 4.*?\s*', '', cleaned_chunk)
                cleaned_chunk = re.sub(r'All words in the image.*?\s*', '', cleaned_chunk)

                # 只保留非空的文本块
                if cleaned_chunk.strip():
                    processed_chunks_with_metadata.append((cleaned_chunk.strip(), metadata))
                else:
                    print(f"跳过一个清理后为空的文本块")

            # 再次检查处理后的文本块
            if not processed_chunks_with_metadata or len(processed_chunks_with_metadata) == 0:
                print(f'Warning: 文件 {file.filename} 处理和清理后没有有效的文本块，跳过此文件')
                continue

            print(f"文件 {file.filename} 最终有效文本块数量: {len(processed_chunks_with_metadata)}")

            # 重新提取处理后的文本和元数据
            processed_chunked_rawtexts = [chunk for chunk, _ in processed_chunks_with_metadata]
            processed_chunked_metadata = [metadata for _, metadata in processed_chunks_with_metadata]

            # 计算tokens
            for chunk in processed_chunked_rawtexts:
                tokens = tokenizer.encode(chunk)
                total_tokens += len(tokens)

            embedded_vectors = embedding_method.embed_documents(processed_chunked_rawtexts)

            # Create Partition
            partition_name = ''.join(e for e in file.filename if e.isalnum())
            created = vectorstore_module_newui.create_vspartition(
                vs_config,
                selected_dataset,
                partition_name=partition_name
            )
            # Generate id_list
            id_list = [partition_name + "_" + str(np.int64(i)) for i in range(1, len(processed_chunked_rawtexts) + 1)]
            # Process each chunk individually
            field_dicts = []
            for i, (chunk_text, metadata, node_id) in enumerate(
                    zip(processed_chunked_rawtexts, processed_chunked_metadata, id_list)):
                chunk_lines = chunk_text.split('\n')

                # Use the metadata directly from the chunk
                start_page = metadata['start_page_num']
                end_page = metadata['end_page_num']
                start_line = metadata['start_line_num']
                end_line = metadata['end_line_num']
                bbox = str(metadata['bbox'])
                content_type = metadata['content_type']

                field_dicts.append({
                    "source": partition_name,
                    "extension": file.filename.split('.')[-1],
                    "language": "US",
                    "permission": "3",
                    "date": datetime.now().strftime("%Y-%m-%d, %H:%M:%S"),
                    "uploader": user_id,
                    "start_page_num": start_page,
                    "end_page_num": end_page,
                    "start_line_num": start_line,
                    "end_line_num": end_line,
                    "node_id": node_id,
                    "content_type": content_type,
                    "bbox": bbox,
                })

            print(f'Total embedding input tokens: {total_tokens}')
            # Insert chunks into graph store
            graph_store.insert_document(processed_chunked_rawtexts, field_dicts, id_list, partition_name)

            # Insert Vectors into Vector Store
            vectorstore_module_newui.insert_vectors_2(
                vs_config,
                selected_dataset,
                partition_name,
                schema_config,
                processed_chunked_rawtexts,
                embedded_vectors,
                field_dicts
            )
            print("inserted document")

            print(f'Successfully Added the Uploaded File <{file.filename}> into Vector Store')

            # Update log file for vector store
            new_file = {"dataset": selected_dataset,
                        "filename": file.filename,
                        "chunk_size": len(processed_chunked_rawtexts),
                        "permission_level": 3,
                        "uploader": user_id,
                        "upload_time": datetime.now().strftime("%Y-%m-%d, %H:%M:%S")}

            # vs_df = vectorstore_module_newui.update_vs_df(new_file)

            # Remove File after Storing into Vector Store
            # os.remove(filename)
            # get_datasets()
            # print(f'Deleted File <{file.filename}> from Upload Folder')

    return jsonify({'status': 'success', 'message': 'File uploaded successfully'})


selected_files = []


@app.route('/selected-files', methods=['POST'])
def point_selected_files():
    global selected_files
    selected_files.clear()  # clear global_file_list

    data = request.get_json()
    print(data)
    selected_files = data['selectedFiles']  # new files in the global_file_list
    # selected_files = [f .split('.')[0]for f in selected_files]
    selected_files = [f for f in selected_files]

    # selected_files = data
    print('Selected Files:')
    print(*selected_files, sep='\n')

    return jsonify({'status': 'success', 'message': 'Selected files received'})


@app.route('/get-datasets', methods=['GET'])
def get_datasets():
    dataset_list = vectorstore_module_newui.init_vsdb(vs_config)
    print(dataset_list)
    return jsonify(dataset_list)


@app.route('/vectorstore/<filename>', methods=['GET'])
def log_file(filename):
    return send_from_directory('vectorstore', filename)


@app.route('/get-files', methods=['GET'])
def get_files(vsdb_log_dir=env.vsdb_log_dir):
    global vs_df
    # Load and return the list of files from file
    vs_df = pd.read_json(vsdb_log_dir)
    unique_files = vs_df['filename'].unique().tolist()
    return jsonify(unique_files)


@app.route('/delete-file', methods=['POST'])
def delete_file():
    global vs_df
    data = request.get_json()
    del_file = data['file_name']
    selected_dataset = data['dataset']
    # Add logic to delete the file from  server
    # os.remove(os.path.join(UPLOAD_FOLDER, file_to_delete))
    print(f"Deleting File: {del_file}")

    # ***** Newly Added
    del_file = ''.join(e for e in del_file if e.isalnum())

    graph_store = graphstore_module_newui(selected_dataset)
    graph_store.delete_document(del_file)
    vectorstore_module_newui.delete_entities(vs_config,
                                             del_file,
                                             selected_dataset)
    print('Deleted File Entities from Vector Store')

    # vs_df = vectorstore_module_newui.delete_vs_df(data['file_name'])
    # print('Deleted File from File Log')
    return jsonify({'status': 'success', 'message': 'File deleted successfully'})


@app.route('/delete-dataset', methods=['POST'])
def delete_dataset():
    global vs_df
    data = request.get_json()
    selected_dataset = data['dataset']
    graph_store = graphstore_module_newui()
    graph_store.delete_dataset(selected_dataset)

    vectorstore_module_newui.delete_collection(vs_config, selected_dataset)
    return jsonify({'message': 'Dataset deleted successfully'})


@app.route('/get-prompt-templates', methods=['GET'])
def get_prompt_templates():
    return jsonify({'prompt_template_list': prompt_template_list})


USER_DATA_DIR = 'user/'


@app.route('/get-all-users-data')
def get_all_users_data():
    all_users_data = {}
    for user_dir in os.listdir(USER_DATA_DIR):
        user_dir_path = os.path.join(USER_DATA_DIR, user_dir)
        if os.path.isdir(user_dir_path):
            user_id = user_dir
            chat_file_path = os.path.join(user_dir_path, f'{user_id}_chat.json')
            try:
                if os.path.exists(chat_file_path):
                    with open(chat_file_path, 'r', encoding='utf-8') as file:
                        user_data = json.load(file)
                        first_entry = next(iter(user_data.values()), None)
                        if first_entry:
                            first_question = first_entry.get('q_msg', 'No question available')
                            question_time = first_entry.get('q_time', 'No time available')
                            all_users_data[user_id] = {
                                'q_msg': first_question,
                                'q_time': question_time,
                            }
                        else:
                            all_users_data[user_id] = {'q_msg': 'No question available', 'q_time': 'No time available'}
            except (IOError, json.JSONDecodeError) as e:
                print(f"Error reading from {chat_file_path}: {e}")
                all_users_data[user_id] = {'q_msg': 'Error loading data', 'q_time': 'Error loading data'}
    return jsonify(all_users_data)


@app.route('/get-user-data/<user_id>')
def get_user_data(user_id):
    user_data_path = os.path.join(USER_DATA_DIR, user_id, f'{user_id}_chat.json')
    if os.path.exists(user_data_path):
        with open(user_data_path, 'r', encoding='utf-8') as file:
            user_data = json.load(file)
            print(user_data)
            return jsonify(user_data)
    else:
        return jsonify({'error': 'User data not found'}), 404


@app.route('/get-user-count', methods=['GET'])
def get_user_count():
    user_dir_path = os.path.join(USER_DATA_DIR)  # Assuming 'user' dir is at the root of your Flask app
    try:
        user_dirs = [name for name in os.listdir(user_dir_path) if os.path.isdir(os.path.join(user_dir_path, name))]
        user_count = len(user_dirs)
        print("user_count: ", user_count)
        return jsonify({'status': 'success', 'user_count': user_count})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})


@app.route('/delete-chat-history', methods=['POST'])
def delete_chat_history():
    data = request.get_json()  # Get the JSON data sent from the frontend
    user_id = data['userId']  # Extract the userId sent from the frontend

    # Define the path to the user's folder
    user_folder_path = os.path.join('user', str(user_id))
    print("user_folder_path: ", user_folder_path)

    # Check if the folder exists and then delete it
    if os.path.exists(user_folder_path):
        try:
            shutil.rmtree(user_folder_path)  # Removes the folder and all its contents
            return jsonify({'message': 'Chat history successfully deleted'}), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500  # Internal Server Error
    else:
        return jsonify({'message': 'User folder not found'}), 404  # Not Found


@app.route('/create-new-chat-history', methods=['POST'])
def create_new_chat_history():
    global user_id
    global chat_history

    user_dir_path = os.path.join(os.getcwd(), 'user')  # Assuming 'user' directory is at the root of your Flask app

    # Ensure the 'user' directory exists
    if not os.path.exists(user_dir_path):
        os.makedirs(user_dir_path)

    # Find the next available user ID by checking existing directories
    user_dirs = [d for d in os.listdir(user_dir_path) if os.path.isdir(os.path.join(user_dir_path, d))]
    user_id = dt.datetime.now().strftime(datetime_userid_format)
    new_user_dir_name = "user-" + user_id

    # next_user_id = len(user_dirs) + 1  # Assuming user IDs are sequential and start from 1
    # new_user_dir_name = f"user-{next_user_id:03d}"  # Format ID as three digits
    new_user_dir_path = os.path.join(user_dir_path, new_user_dir_name)

    # Create the new user directory
    os.makedirs(new_user_dir_path)

    # Create a new chat history JSON file inside the new user directory
    new_chat_history_path = os.path.join(new_user_dir_path, f"{new_user_dir_name}_chat.json")
    chat_history["dir"] = new_chat_history_path
    chat_history["df"] = pd.DataFrame()
    chat_history["langchain"] = ChatMessageHistory()
    chat_history["langchain"].add_user_message("")
    chat_history["langchain"].add_ai_message("")
    chat_history["langchain"] = chat_history["langchain"].messages

    print("Chat History at Create: ", chat_history)

    with open(new_chat_history_path, 'w') as file:
        json.dump({}, file)  # Start with an empty JSON object

    return jsonify({'status': 'success', 'message': f'New chat history for {new_user_dir_name} created successfully.'})


@app.route('/process-selected-chat-history', methods=['POST'])
def process_selected_chat_history():
    data = request.get_json()  # Parse the JSON data sent in the request
    user_id = data.get('userId')
    # Now, you can use `user_id` to perform operations, such as retrieving and sending back the chat history
    # For demonstration, we'll just send back a success message
    return jsonify({'status': 'success', 'message': f'Processed chat history for user ID: {user_id}'})


@app.route('/update-user-session', methods=['POST'])
def update_user_session():
    global user_id
    global user_profile
    global chat_history

    data = request.get_json()
    user_id_str = data.get('userId')
    match = re.search(r'\d+$', user_id_str)

    # Assuming userId is a string that needs to be converted to an integer
    try:
        # raw_uid = int(match.group())
        # uid_digit = config["uid_digit"]
        # user_id = f"%0{uid_digit}d" %raw_uid

        # User Authentication
        # user_fullform = env.user_prefix + str(user_id)

        # user_fullform = env.user_prefix + user_id
        user_fullform = user_id_str
        print("user_fullform: ", user_fullform)
        user_dir = os.path.join(env.user_dir_root, user_fullform)

        # Retrieval of User Profile
        user_profile = os.path.join(user_dir, user_fullform + env.user_prof_suffix)

        # Retrieval of User Chat History
        user_history = os.path.join(user_dir, user_fullform + env.user_chat_suffix)
        chat_history = {"dir": None,
                        "df": None,
                        "langchain": None}

        chat_history["langchain"] = ChatMessageHistory()
        chat_history["dir"] = user_history
        if not os.path.exists(chat_history["dir"]):
            chat_history["df"] = pd.DataFrame()
            chat_history["langchain"].add_user_message("")
            chat_history["langchain"].add_ai_message("")

        else:
            df = pd.read_json(chat_history["dir"], orient="index")
            chat_history["df"] = df
            print("chat_history1:", chat_history)
            # Convert Chat History to langchain Format
            try:
                for q_msg, a_msg in zip(df["q_msg"].to_list(),
                                        df["a_msg"].to_list()):
                    chat_history["langchain"].add_user_message(q_msg)
                    chat_history["langchain"].add_ai_message(a_msg)
                    # print("chat_history:", chat_history)
                    # print("chat_history[langchain]:", chat_history["langchain"])
            except KeyError:
                chat_history["langchain"].add_user_message("")
                chat_history["langchain"].add_ai_message("")

        chat_history["langchain"] = chat_history["langchain"].messages

        return jsonify({'status': 'success', 'message': 'User session updated successfully.'})
    except ValueError:
        return jsonify({'status': 'error', 'message': 'Invalid userId format'}), 400


app.secret_key = 'your_secret_key'


# Existing functions for loading and saving user credentials...

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user_credentials = load_user_credentials()

        if username in user_credentials and check_password_hash(user_credentials[username], password):
            flash('Login successful.', 'success')
            return redirect(url_for('index'))  # Assuming 'index' is the route for your main page
        elif username not in user_credentials:
            flash('Username does not exist.', 'danger')
        else:
            flash('Incorrect password.', 'danger')

    return render_template('login.html')


@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user_credentials = load_user_credentials()

        if username in user_credentials:
            flash('Username already exists.', 'warning')
        else:
            user_credentials[username] = generate_password_hash(password)
            save_user_credentials(user_credentials)
            flash('Registration successful. Please login.', 'success')
            return redirect(url_for('login'))

    return render_template('register.html')


@app.route('/chat', methods=['POST'])
def chat():
    global selected_dataset
    global user_id
    global chat_history

    print("Chat History at Chat: ", chat_history)

    # Check file extension
    def allowed_file(filename):
        return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

    # User Input
    user_input = request.form.get('user_input', '')
    filename_list = request.form.get('filename_list', '')
    print(filename_list)
    mode = request.form.get('mode', 'online')
    # model = request.form.get('model', 'gpt-3.5')
    model = request.form.get('model', 'mixtral')
    rag = request.form.get('rag', 'off')
    selected_dataset = request.form.get('selectedDataset', '')

    include_history = request.form.get('include_history', 'false') == 'true'
    max_history_no = request.form.get('max_history_no', '3')  # Get the value as a string, providing

    print(model)
    print(selected_dataset)
    # Convert max_history_no to integer, using 0 as default if conversion fails
    try:
        max_history_no = int(max_history_no)
    except ValueError:
        max_history_no = 0  # Fallback value if conversion fails

    # *** Refine Model Name on UI Later
    if model.lower() == "gpt-3.5":
        model = "openai"
    elif model.lower() == "llama":
        if rag == "on":
            model = "llama+rag-1"
        else:
            model = "llama-1"
    elif model.lower() == "mixtral":
        if rag == "on":
            model = "mixtral+rag-1"
        else:
            model = "mixtral-1"

    q_time = datetime.now()

    # Initialize bot_response to an empty string or a default value
    bot_response = ""

    # Prompt Engineering
    if len(selected_files) > 1:
        # 多文件情况，使用优化的系统提示词
        multifile_strategy = request.form.get('multifile_strategy', 'priority')
        system_prompt = prompt_module.gen_multifile_system_prompt(system_prompt_para, selected_files,
                                                                  multifile_strategy)
    else:
        # 单文件或无文件情况，使用原有逻辑
        system_prompt = prompt_module.gen_system_prompt(system_prompt_para)  # Using default values

    condense_system_prompt = prompt_module.gen_condense_system_prompt(system_prompt_para)
    prompt_df = pd.read_json(env.prompt_tempte_dir + selected_instrcut + '.json')
    prompt_template = '\n'.join(prompt_df["prompt"])

    # Model Retrieval
    model_name = model.split('-')[0]  # Split Model Number
    prime_model_name = model_name.split('+')[0]  # Split + for RAG
    prime_model_root = os.path.join(env.model_dir_root, prime_model_name)
    model_config_dir = os.path.join(prime_model_root, model + env.model_config_suffix)
    chunks_list = None
    # Check if Model Config File Exists
    if not os.path.exists(model_config_dir):
        err_msg = f" Input Error : The Model ID <{model}> could not be Found in {prime_model_root}"
        print(err_msg)
        bot_response = err_msg

    # Retrieve Model Config File
    else:
        print("Successfully Retrieved Model Config File")
        model_para = pd.read_json(model_config_dir, typ='series')

    # Get retriever for RAG; Return <None> if RAG is OFF
    chunk_list = None
    retriever = None
    files_pages_data = None
    if rag == "on":
        if selected_dataset:
            print(f'RAG is ON. Loaded Dataset {selected_dataset}')

            # 新增：多文件检索策略选择
            multifile_strategy = request.form.get('multifile_strategy', 'priority')  # 默认使用优先级策略

            if len(selected_files) > 1:
                print(f"检测到多文件选择 ({len(selected_files)} 个文件)，使用策略：{multifile_strategy}")

                if multifile_strategy == 'priority':
                    # 使用文件优先级策略
                    retriever, chunks_list = vectorstore_module_newui.get_retriever3_with_file_priority(
                        vs_config, embedding_method, selected_dataset, selected_files, search_config, user_input
                    )
                elif multifile_strategy == 'separated':
                    # 使用文件分离策略（在结果中标注文件来源）
                    retriever, chunks_list = vectorstore_module_newui.get_retriever3_file_separated(
                        vs_config, embedding_method, selected_dataset, selected_files, search_config, user_input
                    )
                else:
                    # 使用原有策略
                    retriever, chunks_list = vectorstore_module_newui.get_retriever3(
                        vs_config, embedding_method, selected_dataset, selected_files, search_config, user_input
                    )
            else:
                # 单文件情况，使用原有逻辑
                retriever, chunks_list = vectorstore_module_newui.get_retriever3(
                    vs_config, embedding_method, selected_dataset, selected_files, search_config, user_input
                )

            files_pages_data = {}
            for chunk in chunks_list:
                filename = chunk['source']
                filename = filename.replace('pdf', '.pdf')
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                page_number = chunk['page_num']
                bbox = chunk['bbox']
                content_type = chunk['content_type']

                if filename not in files_pages_data:
                    files_pages_data[filename] = {}
                if page_number not in files_pages_data[filename]:
                    files_pages_data[filename][page_number] = []
                files_pages_data[filename][page_number].append({"bbox": bbox, "content_type": content_type})
            # from util import drawings_pdf
            # drawings_pdf(chunks_list, selected_dataset)
            # retriever = vectorstore_module_newui.get_retriever2(vs_config,
            #                                              embedding_method,
            #                                              selected_dataset,
            #                                              selected_files,
            #                                              search_config)

            # retriever = vectorstore_module_newui.get_retriever(vs_config,
            #                                              embedding_method,
            #                                              selected_dataset,
            #                                              search_config)
        else:
            print(f'Error : RAG is ON, but Cannot Find Dataset')
            retriever = None
    else:
        print('RAG is OFF')
        retriever = None

    # # Retrieval of Files for RAG Vector Store
    # # Find directory path for RAG; Return <None> if RAG is Off
    # if rag == "on":

    #     # Check whether Files are Selected for RAG
    #     if selected_dataset:
    #         print(f'RAG is ON. Loaded Dataset {selected_dataset}')
    #         rag_dataset = selected_dataset
    #     else:
    #         print(f'Error : RAG is ON, but Cannot Find Dataset')
    #         rag_dataset = []

    # else:
    #     rag_dataset = []

    # Generate AI Response
    print('LLM Model Selected :')
    print(f'- Model UID : {model_para["uid"]}')
    print(f'- Base Model : {model_para["model"]}')
    print(f'- RAG : {retriever is not None}')

    if include_history:
        print("max_history_no: ", max_history_no)
        print("chat_history: ", chat_history["langchain"])
        print("len chat_history: ", len(chat_history["langchain"]) / 2)
        chat_record = chat_history["langchain"]
        if len(chat_history["langchain"]) - 2 > max_history_no:
            n_pairs = max_history_no * 2
            chat_record = chat_history["langchain"][-n_pairs:]
            print("chat_record:", chat_record)

    else:
        chat_record = None

    # if prime_model_name.lower() == "llama":
    #     bot_response = llama_module.llama_response(user_input,
    #                                 system_prompt,
    #                                 condense_system_prompt,
    #                                 prompt_template,
    #                                 model_para,
    #                                 retriever,
    #                                 chat_record)
    if prime_model_name.lower() == "llama":
        bot_response = llama_module.llama_response(user_input,
                                                   system_prompt,
                                                   condense_system_prompt,
                                                   prompt_template,
                                                   model_para,
                                                   retriever,
                                                   chat_record)

    elif prime_model_name.lower() == "openai":
        bot_response = openai_module.openai_response(user_input,
                                                     system_prompt,
                                                     condense_system_prompt,
                                                     prompt_template,
                                                     model_para,
                                                     retriever,
                                                     chat_record)

    elif prime_model_name.lower() == "mixtral":
        bot_response = mixtral_module_deepseek_nochathistory.mixtral_response(
            user_input,
            system_prompt,
            condense_system_prompt,
            prompt_template,
            model_para,
            retriever,
            chat_record)

    # elif prime_model_name.lower() == "instructmixtral":
    #     bot_response = instruct_mixtral_module.mixtral_response(user_input,
    #                                  system_prompt,
    #                                  condense_system_prompt,
    #                                  prompt_template,
    #                                  model_para,
    #                                  mixtral_pipeline,
    #                                  retriever,
    #                                  chat_history["langchain"])
    # Ball sir output json file
    # test_module_2.output_report(bot_response)

    a_time = datetime.now()

    # =============================================================================
    # TWO-STAGE LLM REFERENCE ANALYSIS - NEW FEATURE
    # =============================================================================

    # Apply LLM-based reference analysis if RAG is enabled and chunks exist
    contribution_analysis = None
    enhanced_files_pages_data = files_pages_data

    if rag == "on" and chunks_list and len(chunks_list) > 0:
        try:
            print(f"🤖 Starting Two-Stage LLM reference analysis for {len(chunks_list)} chunks...")
            print(f"📋 Answer length: {len(bot_response)} chars, Question length: {len(user_input)} chars")

            # Generate LLM-based reference analysis
            llm_analysis = generate_llm_based_references(
                user_input, bot_response, chunks_list, model_para, prime_model_name
            )

            if llm_analysis:
                # Process LLM analysis to get contribution scores
                contribution_scores, analysis_summary = process_llm_reference_analysis(llm_analysis, chunks_list)

                # Add contribution scores to chunks with LLM explanations
                enhanced_chunks = add_contribution_scores_to_chunks_llm(chunks_list, contribution_scores, llm_analysis)

                # Format analysis for frontend
                contribution_analysis = format_llm_analysis_for_frontend(enhanced_chunks, analysis_summary)

                # Enhanced files_pages_data with contribution scores and LLM metadata
                enhanced_files_pages_data = {}
                for i, chunk in enumerate(enhanced_chunks):
                    filename = chunk['source']
                    filename = filename.replace('pdf', '.pdf')
                    page_number = chunk['page_num']
                    bbox = chunk['bbox']
                    content_type = chunk['content_type']
                    contribution_score = chunk['contribution_score']
                    contribution_rank = chunk['contribution_rank']

                    # Add LLM-specific metadata
                    llm_explanation = chunk.get('llm_explanation', '')
                    llm_was_used = chunk.get('llm_was_used', False)

                    if filename not in enhanced_files_pages_data:
                        enhanced_files_pages_data[filename] = {}
                    if page_number not in enhanced_files_pages_data[filename]:
                        enhanced_files_pages_data[filename][page_number] = []

                    enhanced_files_pages_data[filename][page_number].append({
                        "bbox": bbox,
                        "content_type": content_type,
                        "contribution_score": contribution_score,
                        "contribution_rank": contribution_rank,
                        "chunk_index": i,
                        "llm_explanation": llm_explanation,
                        "llm_was_used": llm_was_used
                    })

                print(f"✅ Two-Stage LLM reference analysis completed for {len(chunks_list)} chunks")
                print(f"📊 Analysis summary: {contribution_analysis['analysis_summary']}")

            else:
                print("⚠️ LLM reference analysis failed, using fallback method")
                # Fallback to simple scoring when LLM analysis fails
                contribution_scores = calculate_fallback_contribution_scores(chunks_list, bot_response)
                enhanced_chunks = add_contribution_scores_to_chunks(chunks_list, contribution_scores)
                contribution_analysis = format_contribution_analysis_for_frontend(enhanced_chunks)

                # Simple enhanced files_pages_data without LLM metadata
                enhanced_files_pages_data = {}
                for i, chunk in enumerate(enhanced_chunks):
                    filename = chunk['source']
                    filename = filename.replace('pdf', '.pdf')
                    page_number = chunk['page_num']
                    bbox = chunk['bbox']
                    content_type = chunk['content_type']
                    contribution_score = chunk['contribution_score']
                    contribution_rank = chunk['contribution_rank']

                    if filename not in enhanced_files_pages_data:
                        enhanced_files_pages_data[filename] = {}
                    if page_number not in enhanced_files_pages_data[filename]:
                        enhanced_files_pages_data[filename][page_number] = []

                    enhanced_files_pages_data[filename][page_number].append({
                        "bbox": bbox,
                        "content_type": content_type,
                        "contribution_score": contribution_score,
                        "contribution_rank": contribution_rank,
                        "chunk_index": i
                    })

        except Exception as e:
            logger.error(f"❌ Error during Two-Stage LLM reference analysis: {e}")
            contribution_analysis = None
            enhanced_files_pages_data = files_pages_data

    # =============================================================================
    # END TWO-STAGE LLM REFERENCE ANALYSIS
    # =============================================================================

    # Update Chat History
    new_chat = pd.DataFrame([{
        "q_msg": user_input,
        "a_msg": bot_response,
        "q_time": q_time.strftime("%Y-%m-%d %H:%M:%S"),
        "a_time": a_time.strftime("%Y-%m-%d %H:%M:%S"),
        "llm": model,
        "similarity": 1,
        "rating": 3,
        "chunks_list": enhanced_files_pages_data,
        "contribution_analysis": contribution_analysis  # Store analysis in chat history
    }])

    chat_history["df"] = pd.concat([chat_history["df"], new_chat], ignore_index=True)
    chat_history["df"].to_json(chat_history["dir"], orient="index", indent=4)

    new_chat = ChatMessageHistory()
    new_chat.add_user_message(user_input)
    new_chat.add_ai_message(bot_response)
    chat_history["langchain"] += new_chat.messages

    # Return enhanced response with semantic contribution analysis
    response_data = {
        'response': bot_response,
        'chunk_list': enhanced_files_pages_data
    }

    # Add contribution analysis if available
    if contribution_analysis:
        response_data['contribution_analysis'] = contribution_analysis
        response_data['llm_analysis_enabled'] = True
        response_data['analysis_method'] = contribution_analysis.get('analysis_summary', {}).get('analysis_method',
                                                                                                 'unknown')
    else:
        response_data['llm_analysis_enabled'] = False
        response_data['analysis_method'] = 'none'

    return jsonify(response_data)


# pdf file viewer
@app.route('/processed/<filename>')
def Get_file(filename):
    return send_from_directory(app.config['PROCESSED_FOLDER'], filename)

    # def generate_buttons_html(processed_files):
    #     buttons_html = ""
    #     for file in processed_files:
    #         file_name = file['filename']
    #         pdf_path = file['filepath']
    #         button_html = f'''
    #         <button id="reference-button" style="text-align: center; color: black; background: white; font-weight: 700; word-wrap: break-word; font-size: 18px; border-radius:10px; height:40px; margin-right:10px; margin-bottom:10px; display:block;" onclick="showPDFChunkReference('{pdf_path}')">
    #             <div style="display:flex; align-items:center">
    #                 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right:15px">
    #                     <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4C7.34315 4 6 5.34315 6 7V17C6 18.6569 7.34315 20 9 20H15C16.6569 20 18 18.6569 18 17V10H15C13.3431 10 12 8.65685 12 7V4H9ZM14 5.41421L16.5858 8H15C14.4477 8 14 7.55228 14 7V5.41421ZM4 7C4 4.23858 6.23858 2 9 2H13C13.2652 2 13.5196 2.10536 13.7071 2.29289L19.7071 8.29289C19.8946 8.48043 20 8.73478 20 9V17C20 19.7614 17.7614 22 15 22H9C6.23858 22 4 19.7614 4 17V7ZM15.2071 12.2929C15.5976 12.6834 15.5976 13.3166 15.2071 13.7071L12.2071 16.7071C11.8166 17.0976 11.1834 17.0976 10.7929 16.7071L9.29289 15.2071C8.90237 14.8166 8.90237 14.1834 9.29289 13.7929C9.68342 13.4024 10.3166 13.4024 10.7071 13.7929L11.5 14.5858L13.7929 12.2929C14.1834 11.9024 14.8166 11.9024 15.2071 12.2929Z" fill="#F90505"/>
    #                 </svg>
    #                 <div>{file_name}</div>
    #             </div>
    #         </button>
    #         '''
    #         buttons_html += button_html
    #     return buttons_html

    # @app.route('/processed_chunk', methods=['GET', 'POST'])
    # def chunk_extraction():
    data = request.get_json()
    list_of_chunks = data['chunk']
    files_pages_data = {}

    for chunk in list_of_chunks:
        filename = chunk['source']
        filename = filename.replace('pdf', '.pdf')
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        page_number = chunk['page_num']
        bbox = chunk['bbox']
        content_type = chunk['content_type']

        if filename not in files_pages_data:
            files_pages_data[filename] = {}
        if page_number not in files_pages_data[filename]:
            files_pages_data[filename][page_number] = []
        files_pages_data[filename][page_number].append({"bbox": bbox, "content_type": content_type})

    print("Extracted Files, Page Numbers, and Bounding Boxes:", files_pages_data)
    return jsonify({'response': "finish prcessing chunk", 'data_extracted': files_pages_data})


@app.route('/draw_file', methods=['GET', 'POST'])
def draw_bbox():
    """改进的PDF标注函数，支持更多内容类型和更好的视觉效果"""
    data = request.get_json()
    pages_data = data['pages_data']
    filename = data['filename']

    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    doc = fitz.open(file_path)

    # 创建新的PDF文档，只包含有bbox的页面
    new_doc = fitz.open()

    # 定义颜色映射 (RGB)
    color_mapping = {
        'text_with_title': (0, 0.8, 0),  # 绿色
        'text': (0, 0.8, 0),  # 绿色
        'table': (0, 0, 1),  # 蓝色
        'image': (1, 0, 0),  # 红色
        'chart': (1, 0.5, 0),  # 橙色
        'flowchart': (0.5, 0, 1),  # 紫色
        'confusion_matrix': (1, 0, 1)  # 品红色
    }

    print(f"开始处理PDF标注，只保留有bbox的 {len(pages_data)} 页")

    # 按页码排序处理
    sorted_pages = sorted(pages_data.items(), key=lambda x: int(x[0]))

    for page_number_str, bboxes_data in sorted_pages:
        page_number = int(page_number_str)
        page = doc[page_number - 1]  # Adjust for zero-based index

        print(f"处理第 {page_number} 页，共 {len(bboxes_data)} 个区域")

        # 按内容类型分组处理
        for i, bbox_data in enumerate(bboxes_data):
            content_type = bbox_data['content_type']
            bbox_coords = bbox_data['bbox']

            # 获取对应的颜色
            color = color_mapping.get(content_type, (0.5, 0.5, 0.5))  # 默认灰色

            # 创建矩形区域
            rect = fitz.Rect(bbox_coords)

            # 添加彩色矩形边框
            shape = page.new_shape()
            shape.draw_rect(rect)
            shape.finish(color=color, width=3)  # 3像素宽的边框
            shape.commit()

            # 添加半透明填充矩形
            shape2 = page.new_shape()
            shape2.draw_rect(rect)
            shape2.finish(fill=color, fill_opacity=0.1)  # 10%透明度填充
            shape2.commit()

            # 添加标签文本
            label_text = content_type.replace('_', ' ').title()
            label_color = color

            # 计算标签位置
            label_x = bbox_coords[0]
            label_y = bbox_coords[1] - 5

            # 确保标签不会超出页面边界
            if label_y < 15:
                label_y = bbox_coords[1] + 15

            # 添加标签背景
            text_width = len(label_text) * 6  # 估算文本宽度
            label_bg_rect = fitz.Rect(label_x, label_y - 12, label_x + text_width, label_y + 2)
            shape3 = page.new_shape()
            shape3.draw_rect(label_bg_rect)
            shape3.finish(fill=(1, 1, 1), fill_opacity=0.8)  # 白色背景
            shape3.commit()

            # 添加标签文本
            text_point = fitz.Point(label_x + 2, label_y - 2)
            page.insert_text(text_point, label_text, fontsize=10, color=label_color)

            print(f"  - 添加 {content_type} 标注: {bbox_coords}")

        # 将处理好的页面插入到新文档中
        new_doc.insert_pdf(doc, from_page=page_number - 1, to_page=page_number - 1)

    # 保存处理后的文件
    os.makedirs("processed", exist_ok=True)
    processed_file_path = os.path.join("processed", filename)
    new_doc.save(processed_file_path)

    # 关闭文档
    doc.close()
    new_doc.close()

    print(f"✅ 标注完成！只保留有bbox的页面，保存到: {processed_file_path}")

    # 统计各类型数量
    type_counts = {}
    total_pages_with_bbox = len(pages_data)
    for page_data in pages_data.values():
        for bbox_data in page_data:
            content_type = bbox_data['content_type']
            type_counts[content_type] = type_counts.get(content_type, 0) + 1

    print(f"📊 标注统计: {type_counts}")
    print(f"📄 只保留了 {total_pages_with_bbox} 页包含bbox的页面")

    return jsonify({
        'response': "finish drawing enhanced bboxes",
        'processed_file_path': processed_file_path,
        'annotation_statistics': type_counts,
        'total_annotations': sum(type_counts.values()),
        'legend_added': False,
        'pages_with_bbox': total_pages_with_bbox,
        'message': f'只显示包含标注的 {total_pages_with_bbox} 页，无图例页面'
    })


@app.route('/')
def root():
    return redirect(url_for('chat'))


# if __name__ == '__main__':
#     app.run(host='127.0.0.1', port=8008)
# if __name__ == '__main__':
#     app.run(host="localhost", port=5000)

# def open_browser():
#     webbrowser.open_new('http://127.0.0.1:8009')
#
#
# if __name__ == '__main__':
#     threading.Thread(target=open_browser).start()
#
#     app.run(host='0.0.0.0', port=8009)

# class ServerThread(QThread):
#     def __init__(self, flask_app):
#         super().__init__()
#         self.flask_app = flask_app
#         self.srv = make_server('127.0.0.1', 8008, flask_app)
#         self.ctx = flask_app.app_context()
#         self.ctx.push()
#
#     def run(self):
#         self.srv.serve_forever()
#
#     def stop(self):
#         self.srv.shutdown()

# class MainWindow(QMainWindow):
#     def __init__(self):
#         super().__init__()
#         self.setWindowTitle("D.WiseBot")
#         self.setGeometry(750, 75, 2000, 1300)
#         self.web_view = QWebEngineView()
#         self.setCentralWidget(self.web_view)
#
#         self.web_view.load(QUrl("http://127.0.0.1:8008"))
#
#     def closeEvent(self, event):
#         # vectorstore_module_newui.stop_milvue_lite()
#         os.system(r'wmic process where name="milvus.exe_org" call terminate')
#         global flaskThread
#         flaskThread.stop()
#         event.accept()
#
# def start_ollama_service():
#     # 设置模型路径环境变量并启动 ollama 服务
#     model_path = r'C:\Users\<USER>\PycharmProjects\dtt-llm-rag\ollama_models'
#     os.environ['OLLAMA_MODELS'] = model_path
#     subprocess.Popen(['ollama', 'serve'], creationflags=subprocess.CREATE_NEW_CONSOLE)
import psutil


def terminate_ollama_processes():
    for process in psutil.process_iter(['pid', 'name']):
        if process.info['name'] == 'ollama.exe':
            process.terminate()  # 终止找到的ollama进程
            process.wait()  # 等待进程真正终止


def start_ollama_service():
    # 设置模型路径环境变量
    base_path = os.path.dirname(os.path.abspath(__file__))
    model_path = os.path.join(base_path, 'ollama_models')
    os.environ['OLLAMA_MODELS'] = model_path

    # 启动 ollama serve 在新的控制台窗口
    subprocess.Popen(['ollama', 'serve'], creationflags=subprocess.CREATE_NO_WINDOW)


# def run_ollama():
#     # 假设当前脚本位于与 Ollama 目录同一个 U 盘中的某个目录
#     ollama_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Ollama', 'ollama.app.exe')
#     subprocess.call([ollama_path])

# def check_ollama_path():
#     ollama_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Ollama', 'ollama.exe')
#     print("Ollama path:", ollama_path)
#     exists = os.path.exists(ollama_path)
#     print("Does Ollama exist at the specified path?", exists)
#     return ollama_path, exists


def is_ollama_running():
    """检查 ollama.exe 是否正在运行"""
    for process in psutil.process_iter(['name']):
        if process.info['name'] == 'ollama.exe':
            return True
    return False


def try_start_ollama():
    """尝试在系统上启动 ollama.exe"""
    ollama_path = os.path.join('C:', 'Program Files', 'Ollama', 'ollama.exe')  # 根据实际安装路径调整
    try:
        subprocess.Popen([ollama_path], creationflags=subprocess.CREATE_NO_WINDOW)
        return True
    except Exception as e:
        print(f"Failed to start ollama.exe: {e}")
        return False


def generate_json_format_example():
    """
    生成预计算JSON文件的格式示例
    用户可以参考此格式来准备自己的预计算文件
    """
    example_format = {
        "format_description": "预计算布局文件格式示例 - 支持以下三种格式中的任意一种",
        "format_1_simple_list": {
            "description": "格式1: 简单的chunks列表",
            "example": [
                {
                    "chunk_text": "这里是第一个文本块的内容...",
                    "metadata": {
                        "source": "mydocument.pdf",
                        "start_page_num": 1,
                        "end_page_num": 1,
                        "start_line_num": 1,
                        "end_line_num": 5,
                        "content_type": "text",
                        "bbox": [10, 20, 300, 150]
                    }
                },
                {
                    "chunk_text": "这是一个表格内容...",
                    "metadata": {
                        "source": "mydocument.pdf",
                        "start_page_num": 2,
                        "end_page_num": 2,
                        "start_line_num": 1,
                        "end_line_num": 3,
                        "content_type": "table",
                        "bbox": [15, 50, 400, 250]
                    }
                }
            ]
        },
        "format_2_pages_structure": {
            "description": "格式2: 包含页面结构的格式",
            "example": {
                "pages": [
                    {
                        "page_number": 1,
                        "chunks": [
                            {
                                "text": "第一页的文本内容...",
                                "type": "text",
                                "start_line": 1,
                                "end_line": 5,
                                "bbox": [10, 20, 300, 150]
                            }
                        ]
                    },
                    {
                        "page_number": 2,
                        "chunks": [
                            {
                                "text": "第二页的表格内容...",
                                "type": "table",
                                "start_line": 1,
                                "end_line": 3,
                                "bbox": [15, 50, 400, 250]
                            }
                        ]
                    }
                ]
            }
        },
        "format_3_document_level": {
            "description": "格式3: 文档级别的chunks",
            "example": {
                "chunks": [
                    {
                        "text": "文档内容...",
                        "page_number": 1,
                        "type": "text",
                        "start_line": 1,
                        "end_line": 5,
                        "bbox": [10, 20, 300, 150]
                    }
                ]
            }
        },
        "supported_content_types": ["text", "table", "image", "heading", "chart", "flowchart", "confusion_matrix"],
        "bbox_format": "边界框格式: [x1, y1, x2, y2] (左上角和右下角坐标)"
    }
    return example_format


@app.route('/get-precomputed-format-example', methods=['GET'])
def get_precomputed_format_example():
    """提供预计算JSON文件格式示例的API端点"""
    example = generate_json_format_example()
    return jsonify(example)


@app.route('/debug-precomputed-file', methods=['POST'])
def debug_precomputed_file():
    """调试预计算文件的API端点，帮助用户检查文件格式和内容"""
    try:
        data = request.get_json()
        file_path = data.get('file_path', '')

        if not file_path:
            return jsonify({'error': 'Missing file_path parameter'}), 400

        full_path = os.path.join(app.config['UPLOAD_FOLDER'], file_path)

        if not os.path.exists(full_path):
            return jsonify({'error': f'File not found: {file_path}'}), 404

        debug_info = {
            'file_path': file_path,
            'file_exists': True,
            'file_size_bytes': os.path.getsize(full_path)
        }

        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
                debug_info['file_size_chars'] = len(content)

            with open(full_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            debug_info['json_valid'] = True
            debug_info['data_type'] = str(type(json_data).__name__)

            if isinstance(json_data, list):
                debug_info['format'] = 'list (Format 1)'
                debug_info['items_count'] = len(json_data)
                debug_info['sample_item'] = json_data[0] if json_data else None

                # 检查每个项目
                valid_items = 0
                invalid_items = []
                for i, item in enumerate(json_data):
                    if isinstance(item, dict) and 'chunk_text' in item and 'metadata' in item:
                        if item['chunk_text'] and item['chunk_text'].strip():
                            valid_items += 1
                        else:
                            invalid_items.append(f"Item {i + 1}: empty chunk_text")
                    else:
                        invalid_items.append(f"Item {i + 1}: missing required fields")

                debug_info['valid_items'] = valid_items
                debug_info['invalid_items'] = invalid_items[:10]  # 最多显示10个问题

            elif isinstance(json_data, dict):
                if 'pages' in json_data:
                    debug_info['format'] = 'pages structure (Format 2)'
                    debug_info['pages_count'] = len(json_data['pages'])
                elif 'chunks' in json_data:
                    debug_info['format'] = 'document chunks (Format 3)'
                    debug_info['chunks_count'] = len(json_data['chunks'])
                else:
                    debug_info['format'] = 'unknown dictionary format'
                    debug_info['available_keys'] = list(json_data.keys())
            else:
                debug_info['format'] = 'unsupported'

            # 尝试实际处理文件
            chunks_with_metadata, element_counters = process_precomputed_layout(full_path)
            if chunks_with_metadata:
                debug_info['processing_result'] = 'success'
                debug_info['extracted_chunks'] = len(chunks_with_metadata)
                debug_info['element_counters'] = element_counters
            else:
                debug_info['processing_result'] = 'failed'
                debug_info['extracted_chunks'] = 0

        except json.JSONDecodeError as e:
            debug_info['json_valid'] = False
            debug_info['json_error'] = str(e)
        except Exception as e:
            debug_info['processing_error'] = str(e)

        return jsonify(debug_info)

    except Exception as e:
        return jsonify({'error': str(e)}), 500


def process_image_with_ocr(image_path, upload_folder, min_text_length=10, document_name=None):
    """
    使用InternVL处理图像并执行OCR文字识别

    Args:
        image_path: 图像文件路径（相对路径）
        upload_folder: 上传文件夹路径
        min_text_length: 最小文字长度阈值
        document_name: 文档名称（用于构建子文件夹路径）

    Returns:
        tuple: (ocr_text, should_include) - OCR提取的文字和是否应该包含此图像
    """
    try:
        # 构建完整的图像路径 - 图像存储在以文档名命名的子文件夹中
        if document_name:
            # 移除文档的扩展名来构建子文件夹名
            doc_folder_name = os.path.splitext(document_name)[0]
            full_image_path = os.path.join(upload_folder, doc_folder_name, image_path)
        else:
            # 如果没有提供文档名，尝试直接路径
            full_image_path = os.path.join(upload_folder, image_path)

        print(f"DEBUG: 图像路径: {full_image_path}")

        if not os.path.exists(full_image_path):
            print(f"WARNING: 图像文件不存在: {full_image_path}")
            return "", False

        try:
            # 使用InternVL进行OCR处理
            image_base64 = png_image_to_base64_data_uri(full_image_path)

            # 准备InternVL的消息格式
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Please extract and describe all text content visible in this image. If there are charts, tables, or diagrams, describe their content and any visible text or numbers. Focus on extracting readable text accurately."
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": image_base64
                            }
                        }
                    ]
                }
            ]

            # 调用InternVL API
            response = client.chat.completions.create(
                model="OpenGVLab/InternVL3-78B-AWQ",
                messages=messages,
                stream=False,
                max_tokens=1000,
                temperature=0.1
            )

            # 提取OCR文字
            ocr_text = response.choices[0].message.content

            print(f"InternVL OCR result for {image_path}: {len(ocr_text)} characters")
            print(f"OCR Content: {ocr_text[:200]}...")

            # 检查OCR文字长度是否满足要求
            if len(ocr_text.strip()) >= min_text_length:
                print(f"✅ 图像 {image_path} OCR成功，文字长度: {len(ocr_text)}")
                return ocr_text.strip(), True
            else:
                print(f"⚠️  图像 {image_path} OCR文字过短（{len(ocr_text)} < {min_text_length}），跳过")
                return "", False

        except Exception as e:
            print(f"InternVL OCR处理失败: {str(e)}")
            # 使用回退方案
            fallback_text = "this is image content for image"
            print(f"使用回退文本: {fallback_text}")
            return fallback_text, True

    except Exception as e:
        print(f"无法处理图像文件 {image_path}: {str(e)}")
        return "", False


def process_precomputed_layout(json_path: str, markdown_path: str = None) -> List[Tuple[str, Dict]]:
    """
    处理预计算的文档布局文件（JSON和Markdown）

    Args:
        json_path: JSON文件路径，包含文档的结构化数据
        markdown_path: Markdown文件路径（可选，主要用作参考）

    Returns:
        List[Tuple[str, Dict]]: 与chunk_pdf_advanced相同格式的输出
    """
    print(f"正在处理预计算的布局文件: {json_path}")

    try:
        # 读取JSON文件
        with open(json_path, 'r', encoding='utf-8') as f:
            precomputed_data = json.load(f)

        print(f"JSON文件读取成功，数据类型: {type(precomputed_data)}")

        # 检查数据是否为空
        if not precomputed_data:
            print("JSON文件为空")
            return None, None

        chunks_with_metadata = []

        # 检查JSON数据格式
        if isinstance(precomputed_data, list):
            print(f"检测到格式1: 直接chunks列表，包含 {len(precomputed_data)} 个项目")
            # 格式1: 直接是chunks列表
            for i, item in enumerate(precomputed_data):
                if isinstance(item, dict) and 'chunk_text' in item and 'metadata' in item:
                    chunk_text = item['chunk_text']
                    metadata = item['metadata']

                    # 跳过空文本块
                    if not chunk_text or chunk_text.strip() == "":
                        print(f"跳过第 {i + 1} 个空文本块")
                        continue

                    # 确保元数据包含必要的字段
                    required_metadata = {
                        'start_page_num': metadata.get('start_page_num', 1),
                        'end_page_num': metadata.get('end_page_num', 1),
                        'start_line_num': metadata.get('start_line_num', 1),
                        'end_line_num': metadata.get('end_line_num', 1),
                        'content_type': metadata.get('content_type', 'text'),
                        'bbox': metadata.get('bbox', (0, 0, 0, 0)),
                        'source': metadata.get('source', os.path.basename(json_path).replace('.json', ''))
                    }

                    chunks_with_metadata.append((chunk_text, required_metadata))
                    print(f"处理第 {i + 1} 个文本块: {len(chunk_text)} 字符, 类型: {required_metadata['content_type']}")
                else:
                    print(f"跳过第 {i + 1} 个项目: 缺少必要的字段 (chunk_text 或 metadata)")

        elif isinstance(precomputed_data, dict):
            print("检测到字典格式，检查具体结构...")
            # 格式2: 可能包含页面结构的字典格式
            if 'pages' in precomputed_data:
                print(f"检测到格式2: 页面结构格式，包含 {len(precomputed_data['pages'])} 页")
                for page_data in precomputed_data['pages']:
                    page_num = page_data.get('page_number', 1)

                    if 'chunks' in page_data:
                        for i, chunk_data in enumerate(page_data['chunks']):
                            chunk_text = chunk_data.get('text', '')

                            # 跳过空文本块
                            if not chunk_text or chunk_text.strip() == "":
                                print(f"跳过页面 {page_num} 第 {i + 1} 个空文本块")
                                continue

                            metadata = {
                                'start_page_num': page_num,
                                'end_page_num': page_num,
                                'start_line_num': chunk_data.get('start_line', 1),
                                'end_line_num': chunk_data.get('end_line', 1),
                                'content_type': chunk_data.get('type', 'text'),
                                'bbox': chunk_data.get('bbox', (0, 0, 0, 0)),
                                'source': os.path.basename(json_path).replace('.json', '')
                            }

                            chunks_with_metadata.append((chunk_text, metadata))
                            print(f"处理页面 {page_num} 第 {i + 1} 个文本块: {len(chunk_text)} 字符")

            # 格式3: 直接包含文档级别的chunks
            elif 'chunks' in precomputed_data:
                print(f"检测到格式3: 文档级别chunks，包含 {len(precomputed_data['chunks'])} 个项目")
                for i, chunk_data in enumerate(precomputed_data['chunks']):
                    chunk_text = chunk_data.get('text', '')

                    # 跳过空文本块
                    if not chunk_text or chunk_text.strip() == "":
                        print(f"跳过第 {i + 1} 个空文本块")
                        continue

                    metadata = {
                        'start_page_num': chunk_data.get('page_number', 1),
                        'end_page_num': chunk_data.get('page_number', 1),
                        'start_line_num': chunk_data.get('start_line', 1),
                        'end_line_num': chunk_data.get('end_line', 1),
                        'content_type': chunk_data.get('type', 'text'),
                        'bbox': chunk_data.get('bbox', (0, 0, 0, 0)),
                        'source': os.path.basename(json_path).replace('.json', '')
                    }

                    chunks_with_metadata.append((chunk_text, metadata))
                    print(f"处理第 {i + 1} 个文本块: {len(chunk_text)} 字符")

            # 格式4: Miner工具生成的格式（包含pdf_info）
            elif 'pdf_info' in precomputed_data:
                print(f"检测到格式4: Miner工具格式，包含 {len(precomputed_data['pdf_info'])} 页")

                for page_idx, page_info in enumerate(precomputed_data['pdf_info']):
                    page_num = page_info.get('page_idx', page_idx) + 1  # page_idx通常从0开始
                    print(f"处理第 {page_num} 页...")

                    # 统计当前页面的元素
                    page_stats = {
                        'preproc_blocks': len(page_info.get('preproc_blocks', [])),
                        'para_blocks': len(page_info.get('para_blocks', [])),
                        'images': len(page_info.get('images', [])),
                        'tables': len(page_info.get('tables', [])),
                        'text_found': 0
                    }
                    print(f"页面统计: {page_stats}")

                    # 用于去重的集合，存储已处理的内容hash
                    processed_content_hashes = set()

                    # 处理所有可能包含文本的块（改进的去重逻辑）
                    text_blocks_to_process = []

                    # 优先处理preproc_blocks（通常是最完整的）
                    if 'preproc_blocks' in page_info:
                        for block in page_info['preproc_blocks']:
                            text_blocks_to_process.append(('preproc', block))

                    # 只有当preproc_blocks不存在或为空时才处理para_blocks
                    elif 'para_blocks' in page_info:
                        for block in page_info['para_blocks']:
                            text_blocks_to_process.append(('para', block))

                    # 添加images中的图像（确保类型正确）
                    if 'images' in page_info:
                        for image in page_info['images']:
                            text_blocks_to_process.append(('image', image))

                    # 添加tables中的表格（独立处理，因为结构不同）
                    if 'tables' in page_info:
                        for table in page_info['tables']:
                            text_blocks_to_process.append(('table', table))

                    # 处理所有文本块
                    for block_idx, (source_type, block) in enumerate(text_blocks_to_process):
                        content_type = block.get('type', 'text')
                        bbox = block.get('bbox', [0, 0, 0, 0])
                        original_type_from_json = content_type

                        # 改进的文本提取，避免重复
                        block_text = extract_text_from_block_improved(block)

                        # 根据实际内容和source_type正确设置content_type
                        if source_type == 'table' or content_type == 'table':
                            content_type = 'table'
                            # 尝试提取表格的结构化文本
                            if not block_text and 'cells' in block:
                                table_text = ""
                                for cell in block.get('cells', []):
                                    cell_text = extract_text_from_block_improved(cell)
                                    if cell_text:
                                        table_text += cell_text + " | "
                                if table_text:
                                    block_text = table_text
                        elif content_type == 'image' or block_text == "image content":
                            content_type = 'image'

                            # 处理图像OCR - 从嵌套结构中查找image_path
                            image_path = ''

                            # 方法1: 直接在block中查找image_path
                            if 'image_path' in block:
                                image_path = block['image_path']

                            # 方法2: 在lines/spans结构中查找image_path
                            elif 'lines' in block:
                                for line in block['lines']:
                                    if 'spans' in line:
                                        for span in line['spans']:
                                            if span.get('type') == 'image' and 'image_path' in span:
                                                image_path = span['image_path']
                                                break
                                    if image_path:
                                        break

                            # 方法3: 在spans中直接查找（如果没有lines包装）
                            elif 'spans' in block:
                                for span in block['spans']:
                                    if span.get('type') == 'image' and 'image_path' in span:
                                        image_path = span['image_path']
                                        break

                            # 方法4: 在blocks/lines/spans嵌套结构中查找
                            elif 'blocks' in block:
                                for sub_block in block['blocks']:
                                    if 'lines' in sub_block:
                                        for line in sub_block['lines']:
                                            if 'spans' in line:
                                                for span in line['spans']:
                                                    if span.get('type') == 'image' and 'image_path' in span:
                                                        image_path = span['image_path']
                                                        break
                                            if image_path:
                                                break

                            print(
                                f"DEBUG: 查找图像路径结果 - image_path: '{image_path}', block keys: {list(block.keys())}")

                            if image_path:
                                upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
                                # 获取文档名称用于构建子文件夹路径
                                document_name = os.path.basename(json_path).replace('.json', '')
                                ocr_text, should_include = process_image_with_ocr(image_path, upload_folder,
                                                                                  min_text_length=10,
                                                                                  document_name=document_name)

                                if should_include and ocr_text:
                                    # 用OCR文字替换"image content"
                                    block_text = ocr_text
                                    print(f"✅ 图像 {image_path} OCR成功，文字长度: {len(ocr_text)}")
                                else:
                                    # 跳过此图像块
                                    print(f"⚠️  跳过图像 {image_path} - 文字不足或处理失败")
                                    continue
                            else:
                                print(f"⚠️  图像块缺少image_path信息，跳过")
                                continue
                        elif block_text and block_text.strip().startswith('<html>'):
                            content_type = 'table'

                        # 调试打印，显示原始类型和最终确定的类型
                        print(
                            f"DEBUG Layout Processing: Page {page_num}, Block {block_idx + 1} - Original Type: '{original_type_from_json}', Final Type: '{content_type}', Content: '{block_text[:200]}...'")

                        # 去重检查：计算内容hash
                        if block_text.strip():
                            # 检查是否为模板性重复内容
                            def is_template_content(text):
                                """检查是否为模板性内容"""
                                import re
                                text_clean = text.strip()
                                if len(text_clean) < 5:  # 过短的内容可能是模板
                                    return True
                                template_patterns = [
                                    r'^Note:?\s*$',
                                    r'^Remarks?:?\s*$',
                                    r'^Notation\s+圖例\s*$',
                                    r'^\d+\s*NOTES?\s+TO\s+PURCHASERS?\s*',
                                    r'^重要事項\s*$',
                                    r'^免責聲明\s*$'
                                ]
                                for pattern in template_patterns:
                                    if re.match(pattern, text_clean, re.IGNORECASE):
                                        return True
                                return False

                            content_hash = hash(block_text.strip())
                            if content_hash in processed_content_hashes:
                                print(f"⚠️  第 {page_num} 页第 {block_idx + 1} 个块 ({source_type}) 重复内容，跳过")
                                continue

                            # 对于模板性内容，增加页面信息到hash中避免过度去重
                            if is_template_content(block_text.strip()):
                                content_hash = hash(f"{block_text.strip()}_{page_num}")
                                print(
                                    f"🔄 第 {page_num} 页第 {block_idx + 1} 个块 ({source_type}) 检测为模板内容，使用页面特定hash")

                            processed_content_hashes.add(content_hash)

                            metadata = {
                                'start_page_num': page_num,
                                'end_page_num': page_num,
                                'start_line_num': block_idx + 1,
                                'end_line_num': block_idx + 1,
                                'content_type': content_type,
                                'bbox': tuple(bbox) if isinstance(bbox, list) else bbox,
                                'source': os.path.basename(json_path).replace('.json', '')
                            }

                            chunks_with_metadata.append((block_text.strip(), metadata))
                            page_stats['text_found'] += 1
                            print(
                                f"✅ 第 {page_num} 页第 {block_idx + 1} 个块 ({source_type}): {len(block_text.strip())} 字符, 类型: {content_type}")
                        else:
                            print(
                                f"⚠️  第 {page_num} 页第 {block_idx + 1} 个块 ({source_type}) 无文本内容, 类型: {content_type}")

                    print(f"第 {page_num} 页完成，提取到 {page_stats['text_found']} 个文本块")

            else:
                print("无法识别的字典格式，缺少 'pages'、'chunks' 或 'pdf_info' 键")
                print(f"可用的顶级键: {list(precomputed_data.keys())}")
                return None, None

        else:
            print(f"不支持的数据格式: {type(precomputed_data)}")
            return None, None

        if len(chunks_with_metadata) == 0:
            print("警告: 没有找到有效的文本块")
            return None, None

        print(f"成功处理预计算布局文件，共提取 {len(chunks_with_metadata)} 个文本块")

        # 合并短的文本块
        chunks_with_metadata = merge_short_text_chunks(chunks_with_metadata)

        # 注意：重叠功能现在在主流程中统一处理，这里不再添加重叠

        # 返回格式：(chunks_with_metadata, element_counters)
        # 计算元素计数器
        element_counters = {"image": 0, "table": 0, "chart": 0, "flowchart": 0, "confusion_matrix": 0}
        for _, metadata in chunks_with_metadata:
            content_type = metadata.get('content_type', 'text')
            if content_type in element_counters:
                element_counters[content_type] += 1

        print(f"元素统计: {element_counters}")
        return chunks_with_metadata, element_counters

    except FileNotFoundError:
        print(f"错误: 找不到文件 {json_path}")
        return None, None
    except json.JSONDecodeError as e:
        print(f"错误: JSON格式无效 - {str(e)}")
        return None, None
    except Exception as e:
        print(f"处理预计算布局文件时出错: {str(e)}")
        print(f"将回退到原始PDF处理方式")
        return None, None


def test_chunk_overlap_functionality():
    """
    测试重叠功能是否正确工作
    """
    print("🧪 测试重叠功能...")

    # 创建测试数据
    test_chunks = [
        ("这是第一个文本块的内容，包含一些重要的信息。", {
            'start_page_num': 1, 'end_page_num': 1, 'start_line_num': 1, 'end_line_num': 1,
            'content_type': 'text', 'bbox': (0, 0, 100, 50), 'source': 'test.pdf'
        }),
        ("这是第二个文本块，继续讲述更多的内容和细节。", {
            'start_page_num': 1, 'end_page_num': 1, 'start_line_num': 2, 'end_line_num': 2,
            'content_type': 'text', 'bbox': (0, 50, 100, 100), 'source': 'test.pdf'
        }),
        ("第三个文本块提供了额外的重要信息和总结。", {
            'start_page_num': 1, 'end_page_num': 1, 'start_line_num': 3, 'end_line_num': 3,
            'content_type': 'text', 'bbox': (0, 100, 100, 150), 'source': 'test.pdf'
        })
    ]

    print("原始文本块:")
    for i, (text, metadata) in enumerate(test_chunks):
        print(f"  块 {i + 1}: {text}")

    # 应用重叠功能
    overlapped_chunks = add_chunk_overlap(test_chunks, overlap_words=5, overlap_chars=20)

    print("\n应用重叠后的文本块:")
    for i, (text, metadata) in enumerate(overlapped_chunks):
        print(f"  块 {i + 1}: {text}")

    # 验证重叠是否正确
    if len(overlapped_chunks) >= 2:
        first_chunk = overlapped_chunks[0][0]
        second_chunk = overlapped_chunks[1][0]

        # 检查第二个块是否包含第一个块的结尾部分
        first_words = first_chunk.split()[-5:]  # 第一个块的最后5个词
        overlap_found = False

        for word in first_words:
            if word in second_chunk:
                overlap_found = True
                break

        if overlap_found:
            print("✅ 重叠功能正常工作!")
        else:
            print("❌ 重叠功能可能有问题!")
    else:
        print("⚠️  测试文本块数量不足")

    return overlapped_chunks


# 添加新的API端点，专门处理chunks_info格式的PDF标注
@app.route('/annotate_pdf_with_chunks', methods=['POST'])
def annotate_pdf_with_chunks():
    """
    使用chunks_info数据标注PDF文件
    兼容pdf_annotator_improved.py的功能
    """
    data = request.get_json()
    filename = data.get('filename')
    chunks_info = data.get('chunks_info', [])
    add_legend = data.get('add_legend', False)  # 默认不添加图例

    if not filename or not chunks_info:
        return jsonify({'error': 'Missing filename or chunks_info'}), 400

    try:
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        if not os.path.exists(file_path):
            return jsonify({'error': f'File not found: {filename}'}), 404

        # 打开原始PDF文件
        original_pdf = fitz.open(file_path)

        # 创建新的PDF文档，只包含有bbox的页面
        pdf_document = fitz.open()

        print(f"原始PDF共有 {original_pdf.page_count} 页")
        print(f"将处理 {len(chunks_info)} 个区域")

        # 定义颜色映射 (RGB)
        color_mapping = {
            'text_with_title': (0, 0.8, 0),  # 绿色
            'text': (0, 0.8, 0),  # 绿色
            'table': (0, 0, 1),  # 蓝色
            'image': (1, 0, 0),  # 红色
            'chart': (1, 0.5, 0),  # 橙色
            'flowchart': (0.5, 0, 1),  # 紫色
            'confusion_matrix': (1, 0, 1)  # 品红色
        }

        # 按页面分组chunks
        chunks_by_page = {}
        for chunk in chunks_info:
            page_num = chunk['page_num']
            if page_num not in chunks_by_page:
                chunks_by_page[page_num] = []
            chunks_by_page[page_num].append(chunk)

        # 按页码排序处理，只处理有bbox的页面
        sorted_pages = sorted(chunks_by_page.items())

        for page_num, page_chunks in sorted_pages:
            # 注意：fitz使用0基索引，而我们的数据使用1基索引
            page_index = page_num - 1

            if page_index >= original_pdf.page_count:
                print(f"警告：页面 {page_num} 超出PDF范围，跳过")
                continue

            # 从原始PDF复制页面到新PDF
            pdf_document.insert_pdf(original_pdf, from_page=page_index, to_page=page_index)

            # 获取新PDF中的对应页面（最后插入的页面）
            page = pdf_document[pdf_document.page_count - 1]

            print(f"处理第 {page_num} 页，共 {len(page_chunks)} 个区域")

            # 在该页面上添加所有标注
            for i, chunk in enumerate(page_chunks):
                bbox = chunk['bbox']
                content_type = chunk['content_type']

                # 获取对应的颜色
                color = color_mapping.get(content_type, (0.5, 0.5, 0.5))  # 默认灰色

                # 创建矩形区域
                rect = fitz.Rect(bbox[0], bbox[1], bbox[2], bbox[3])

                # 添加彩色矩形边框
                shape = page.new_shape()
                shape.draw_rect(rect)
                shape.finish(color=color, width=3)  # 3像素宽的边框
                shape.commit()

                # 添加半透明填充矩形
                shape2 = page.new_shape()
                shape2.draw_rect(rect)
                shape2.finish(fill=color, fill_opacity=0.1)  # 10%透明度填充
                shape2.commit()

                # 添加标签文本
                label_text = content_type.replace('_', ' ').title()
                label_color = color

                # 计算标签位置
                label_x = bbox[0]
                label_y = bbox[1] - 5

                # 确保标签不会超出页面边界
                if label_y < 15:
                    label_y = bbox[1] + 15

                # 添加标签背景
                text_width = len(label_text) * 6  # 估算文本宽度
                label_bg_rect = fitz.Rect(label_x, label_y - 12, label_x + text_width, label_y + 2)
                shape3 = page.new_shape()
                shape3.draw_rect(label_bg_rect)
                shape3.finish(fill=(1, 1, 1), fill_opacity=0.8)  # 白色背景
                shape3.commit()

                # 添加标签文本
                text_point = fitz.Point(label_x + 2, label_y - 2)
                page.insert_text(text_point, label_text, fontsize=10, color=label_color)

                print(f"  - 添加 {content_type} 标注: {bbox}")

        # 可选择性添加颜色图例页面
        if add_legend and pdf_document.page_count > 0:
            print("添加颜色图例页面...")
            create_color_legend_page_in_doc(pdf_document)

        # 保存标注后的PDF
        os.makedirs("processed", exist_ok=True)
        base_name = os.path.splitext(filename)[0]
        output_filename = f"{base_name}_annotated.pdf"
        output_path = os.path.join("processed", output_filename)

        pdf_document.save(output_path)
        original_pdf.close()
        pdf_document.close()

        pages_with_bbox = len(chunks_by_page)
        message = f'PDF annotation completed - 只显示包含标注的 {pages_with_bbox} 页'
        if not add_legend:
            message += '，无图例页面'

        print(f"标注完成！只保留有bbox的 {pages_with_bbox} 页，新PDF已保存为: {output_path}")

        # 统计信息
        type_count = {}
        for chunk in chunks_info:
            content_type = chunk['content_type']
            type_count[content_type] = type_count.get(content_type, 0) + 1

        return jsonify({
            'status': 'success',
            'message': message,
            'output_file': output_filename,
            'output_path': output_path,
            'annotation_statistics': type_count,
            'total_annotations': len(chunks_info),
            'legend_added': add_legend,
            'pages_with_bbox': pages_with_bbox,
            'original_total_pages': original_pdf.page_count if 'original_pdf' in locals() else 0
        })

    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        return jsonify({'error': str(e)}), 500


def create_color_legend_page_in_doc(pdf_document):
    """在PDF文档中创建颜色图例页面"""

    # 插入新页面作为图例页
    legend_page = pdf_document.new_page(0, width=595, height=842)  # A4尺寸，插入到开头

    # 标题
    title_point = fitz.Point(50, 50)
    legend_page.insert_text(title_point, "内容类型颜色图例 / Content Type Color Legend", fontsize=18, color=(0, 0, 0))

    # 图例项目
    legend_items = [
        ("text_with_title", "标题文本 / Text With Title", (0, 0.8, 0)),
        ("text", "普通文本 / Plain Text", (0, 0.8, 0)),
        ("table", "表格 / Table", (0, 0, 1)),
        ("image", "图片 / Image", (1, 0, 0)),
        ("chart", "图表 / Chart", (1, 0.5, 0)),
        ("flowchart", "流程图 / Flowchart", (0.5, 0, 1)),
        ("confusion_matrix", "混淆矩阵 / Confusion Matrix", (1, 0, 1))
    ]

    y_start = 100
    for i, (type_name, chinese_name, color) in enumerate(legend_items):
        y_pos = y_start + i * 40

        # 绘制颜色方块
        color_rect = fitz.Rect(50, y_pos, 85, y_pos + 25)
        shape = legend_page.new_shape()
        shape.draw_rect(color_rect)
        shape.finish(color=color, width=2, fill=color, fill_opacity=0.3)
        shape.commit()

        # 添加说明文字
        text_point = fitz.Point(100, y_pos + 18)
        legend_page.insert_text(text_point, f"{chinese_name}", fontsize=13, color=(0, 0, 0))

    # 添加使用说明
    instruction_y = y_start + len(legend_items) * 40 + 60
    instruction_text = [
        "使用说明 / Instructions:",
        "• 每个内容区域都用相应颜色的边框和半透明填充标记",
        "• Each content area is marked with colored border and semi-transparent fill",
        "• 标签显示在每个区域的上方或内部",
        "• Labels are displayed above or inside each area",
        "• text_with_title 和 text 都使用绿色，但可能有不同的深浅",
        "• text_with_title and text both use green color with possible variations",
        "",
        "⚠️ 重要提示 / Important Note:",
        "• 此PDF只包含有标注的页面，不包含原始PDF的所有页面",
        "• This PDF only contains pages with annotations, not all pages from original PDF",
        "• 页面顺序按原始页码排列",
        "• Pages are ordered by original page numbers",
        "",
        f"生成时间 / Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    ]

    for i, instruction in enumerate(instruction_text):
        if instruction:  # 跳过空行
            text_point = fitz.Point(50, instruction_y + i * 22)
            if instruction.startswith("⚠️"):
                font_size = 14
                color = (0.8, 0.4, 0)  # 橙色警告
            elif i == 0:
                font_size = 15
                color = (0, 0, 0)
            else:
                font_size = 11
                color = (0.4, 0.4, 0.4)
            legend_page.insert_text(text_point, instruction, fontsize=font_size, color=color)


if __name__ == '__main__':
    # 添加HTML表格解析测试函数
    def test_html_table_parsing():
        """
        测试HTML表格解析功能
        """
        html_table = """<html><body><table><tr><td>Name of the Development</td><td>發展項目名稱</td></tr><tr><td>Amber Place</td><td>恒珀</td></tr><tr><td>The name of the street at which the Development is situated and the street number allocated by the Commissioner of Rating and Valuation for the purpose of distinguishing the Development</td><td>發展項目所位於的街道的名稱及由差餉物業估價署署長為識別發展項目的目的而 編配的門牌號數 昌華街1號</td></tr><tr><td>No.1 Cheung Wah Street</td><td></td></tr><tr><td>The Development consists of one multi-unit building</td><td>發展項目包含一幢多單位建築物</td></tr><tr><td>Total number of storeys of the multi-unit building 26 (exclusive of Roof, Upper Roof 1, Upper Roof 2 and Top Roof)</td><td>該幢多單位建築物的樓層的總數 26 層 (不包括天台丶上層天台1、上層天台2及頂層天台)</td></tr><tr><td>The floor numbering in the multi-unit building as provided in the approved</td><td></td></tr><tr><td>building plans for the Development G/F,1/F -3/F, U3/F,5/F-12/F,15/F -23/F,25/F- 28/F,R0of, Upper Ro0f1,Upper Roof 2 and Top Roof</td><td>發展項目的經批准的建築圖則所規定的該幢多單位建築物內的樓層號數 地下、1樓至3樓`U3樓`5樓至12樓`15樓至23樓`25樓至28樓、天台` 上層天台1、上層天台2及頂層天台</td></tr><tr><td>Omitted floor numbers in the multi-unit building in which the floor numbering is</td><td>有不依連續次序的樓層號數的該幢多單位建築物內被略去的樓層號數</td></tr><tr><td>not in consecutive order 4/F,13/F,14/F and 24/F are omitted</td><td>不設4樓、13樓、14樓及24樓</td></tr><tr><td></td><td></td></tr><tr><td>Refuge floor of the multi-unit building</td><td>該幢多單位建築物内的庇護層</td></tr></table></body></html>"""

        print("原始HTML表格:")
        print(html_table)
        print("\n" + "=" * 80 + "\n")

        # 测试格式化文本输出
        formatted_text = parse_html_table_to_text(html_table)
        print("转换为格式化文本:")
        print(formatted_text)
        print("\n" + "=" * 80 + "\n")

        # 测试Markdown输出
        markdown_text = convert_html_table_to_markdown(html_table)
        print("转换为Markdown格式:")
        print(markdown_text)

        return formatted_text, markdown_text


    # 如果直接运行此脚本，执行测试
    print("测试HTML表格解析功能...")
    test_html_table_parsing()

    print("\n" + "=" * 80 + "\n")

    print("测试重叠功能...")
    test_chunk_overlap_functionality()

    # 原始的应用启动代码
    app.run(host='0.0.0.0', port=8042)